<?php
    $schema = [
        "@context" => "https://schema.org",
        "@type" => "BreadcrumbList",
        "itemListElement" => array_map(function ($breadcrumb, $index) {
            return [
                "@type" => "ListItem",
                "position" => $index + 1,
                "name" => $breadcrumb['name'],
                "item" => $breadcrumb['url']
            ];
        }, $breadcrumbs, array_keys($breadcrumbs))
    ];
?>

<script type="application/ld+json">
    <?php echo json_encode($schema, JSON_PRETTY_PRINT); ?>

</script><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/pincodes/json-ld/breadcrumbs.blade.php ENDPATH**/ ?>