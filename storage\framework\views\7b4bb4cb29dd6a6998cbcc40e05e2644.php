<?php if (isset($component)) { $__componentOriginal04f5fbed4767982b0c5034a9d937303f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal04f5fbed4767982b0c5034a9d937303f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.pincode-reviews','data' => ['reviews' => $reviews,'pincode' => $pincodes,'hasMoreReviews' => $hasMoreReviews,'totalReviewsCount' => $totalReviewsCount,'showForm' => true,'showViewAllButton' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('pincode-reviews'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['reviews' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($reviews),'pincode' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($pincodes),'hasMoreReviews' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($hasMoreReviews),'totalReviewsCount' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($totalReviewsCount),'showForm' => true,'showViewAllButton' => true]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal04f5fbed4767982b0c5034a9d937303f)): ?>
<?php $attributes = $__attributesOriginal04f5fbed4767982b0c5034a9d937303f; ?>
<?php unset($__attributesOriginal04f5fbed4767982b0c5034a9d937303f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal04f5fbed4767982b0c5034a9d937303f)): ?>
<?php $component = $__componentOriginal04f5fbed4767982b0c5034a9d937303f; ?>
<?php unset($__componentOriginal04f5fbed4767982b0c5034a9d937303f); ?>
<?php endif; ?>

<?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/pincodes/partials/reviews.blade.php ENDPATH**/ ?>