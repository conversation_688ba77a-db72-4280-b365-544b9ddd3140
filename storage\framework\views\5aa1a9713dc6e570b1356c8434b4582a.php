<?php $__env->startSection('content'); ?>
    <div class="bg-bg-light dark:bg-bg-dark min-h-screen">
        
        <div class="container mx-auto px-4 py-16 text-center max-w-6xl">
            <h1 class="text-4xl md:text-5xl font-bold text-text-primary-light dark:text-text-primary-dark mb-6">
                <?php echo e(get_setting('contact_header')); ?>

            </h1>
            <p class="text-xl text-text-secondary-light dark:text-text-secondary-dark max-w-3xl mx-auto">
                <?php echo e(get_setting('contact_content')); ?>

            </p>
            <div class="hidden"><?php echo e($title ?? 'Welcome to BharatPostal Info'); ?></div>
        </div>

        
        <div class="container mx-auto px-4 max-w-6xl">
            <div class="grid md:grid-cols-2 gap-12">
                
                <div class="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-md border border-border-light dark:border-border-dark">
                    <h2 class="text-2xl font-semibold text-text-primary-light dark:text-text-primary-dark mb-6">
                        <?php echo e(get_setting('contact_get_in_touch')); ?>

                    </h2>
                    <div class="space-y-6">
                        
                        <div class="flex items-start space-x-4">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                stroke="currentColor" class="w-6 h-6 text-primary-light dark:text-primary-dark flex-shrink-0">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
                            </svg>
                            <div>
                                <h3 class="font-semibold text-text-primary-light dark:text-text-primary-dark">Address</h3>
                                <p class="text-text-secondary-light dark:text-text-secondary-dark">
                                    <?php echo e(get_setting('contact_address')); ?>

                                </p>
                            </div>
                        </div>

                        
                        <div class="flex items-start space-x-4">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                stroke="currentColor" class="w-6 h-6 text-primary-light dark:text-primary-dark flex-shrink-0">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M2.25 6.75c0 8.284 6.716 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z" />
                            </svg>
                            <div>
                                <h3 class="font-semibold text-text-primary-light dark:text-text-primary-dark">Phone</h3>
                                <p class="text-text-secondary-light dark:text-text-secondary-dark">
                                    <?php echo e(get_setting('contact_phone')); ?>

                                </p>
                            </div>
                        </div>

                        
                        <div class="flex items-start space-x-4">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                stroke="currentColor" class="w-6 h-6 text-primary-light dark:text-primary-dark flex-shrink-0">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                            </svg>
                            <div>
                                <h3 class="font-semibold text-text-primary-light dark:text-text-primary-dark">Email</h3>
                                <p class="text-text-secondary-light dark:text-text-secondary-dark space-y-1">
                                    <span class="block"> <?php echo e(get_setting('contact_email')); ?></span>
                                    <span class="block"> <?php echo e(get_setting('contact_email_alternate')); ?></span>
                                </p>
                            </div>
                        </div>

                        
                        <div class="flex items-start space-x-4">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                stroke="currentColor" class="w-6 h-6 text-primary-light dark:text-primary-dark flex-shrink-0">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <div>
                                <h3 class="font-semibold text-text-primary-light dark:text-text-primary-dark">Business Hours</h3>
                                <p class="text-text-secondary-light dark:text-text-secondary-dark">
                                    <?php echo e(get_setting('contact_biz_hours_1')); ?><br>
                                    <?php echo e(get_setting('contact_biz_hours_2')); ?><br>
                                    <?php echo e(get_setting('contact_biz_hours_3')); ?>

                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                
                <div>
                    <form action="<?php echo e(route('contact.submit')); ?>" method="POST" class="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-md border border-border-light dark:border-border-dark">
                        <?php echo csrf_field(); ?>
                        <h2 class="text-2xl font-semibold text-text-primary-light dark:text-text-primary-dark mb-6">
                            <?php echo e(get_setting('contact_send_message')); ?>

                        </h2>

                        
                        <div class="mb-4">
                            <label for="name" class="block text-text-primary-light dark:text-text-primary-dark font-medium mb-2">
                                Full Name
                            </label>
                            <input type="text" id="name" name="name" required
                                class="w-full px-4 py-2 border border-border-light dark:border-border-dark rounded-md focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark bg-white dark:bg-gray-700 text-text-primary-light dark:text-text-primary-dark"
                                placeholder="Enter your full name">
                        </div>

                        
                        <div class="mb-4">
                            <label for="email" class="block text-text-primary-light dark:text-text-primary-dark font-medium mb-2">
                                Email Address
                            </label>
                            <input type="email" id="email" name="email" required
                                class="w-full px-4 py-2 border border-border-light dark:border-border-dark rounded-md focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark bg-white dark:bg-gray-700 text-text-primary-light dark:text-text-primary-dark"
                                placeholder="Enter your email">
                        </div>

                        
                        <div class="mb-4">
                            <label for="phone" class="block text-text-primary-light dark:text-text-primary-dark font-medium mb-2">
                                Phone Number (Optional)
                            </label>
                            <input type="tel" id="phone" name="phone"
                                class="w-full px-4 py-2 border border-border-light dark:border-border-dark rounded-md focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark bg-white dark:bg-gray-700 text-text-primary-light dark:text-text-primary-dark"
                                placeholder="Enter your phone number">
                        </div>

                        
                        <div class="mb-4">
                            <label for="subject" class="block text-text-primary-light dark:text-text-primary-dark font-medium mb-2">
                                Subject
                            </label>
                            <select id="subject" name="subject" required
                                class="w-full px-4 py-2 border border-border-light dark:border-border-dark rounded-md focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark bg-white dark:bg-gray-700 text-text-primary-light dark:text-text-primary-dark">
                                <option value="">Select a subject</option>
                                <option value="support">Customer Support</option>
                                <option value="sales">Sales Inquiry</option>
                                <option value="partnership">Partnership</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        
                        <div class="mb-6">
                            <label for="message" class="block text-text-primary-light dark:text-text-primary-dark font-medium mb-2">
                                Your Message
                            </label>
                            <textarea id="message" name="message" rows="4" required
                                class="w-full px-4 py-2 border border-border-light dark:border-border-dark rounded-md focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark bg-white dark:bg-gray-700 text-text-primary-light dark:text-text-primary-dark"
                                placeholder="Write your message here..."></textarea>
                        </div>

                        
                        <button type="submit"
                            class="w-full bg-primary-light dark:bg-primary-dark text-white py-3 rounded-md hover:bg-primary-dark dark:hover:bg-accent-dark transition duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:focus:ring-primary-dark">
                            Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="container mx-auto px-4 max-w-6xl">
            <?php if(session('success')): ?>
                <div class="bg-green-100 dark:bg-green-900/30 border border-green-400 dark:border-green-600 text-green-700 dark:text-green-300 px-4 py-3 rounded">
                    <?php echo e(session('success')); ?>

                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="bg-red-100 dark:bg-red-900/30 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-4 py-3 rounded">
                    <?php echo e(session('error')); ?>

                </div>
            <?php endif; ?>
        </div>

        
        <div class="container mx-auto px-4 py-16 max-w-6xl">
            <h2 class="text-3xl font-semibold text-center mb-8 text-text-primary-light dark:text-text-primary-dark">
                Our Location
            </h2>
            <div class="bg-border-light dark:bg-border-dark rounded-lg overflow-hidden shadow-md">
                <?php if(get_setting('contact_map_iframe')): ?>
                    <?php echo get_setting('contact_map_iframe'); ?>

                <?php else: ?>
                    <div class="p-4 text-center text-text-secondary-light dark:text-text-secondary-dark">
                        No map location has been set yet.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        // Optional: Client-side form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const name = document.getElementById('name');
            const email = document.getElementById('email');
            const message = document.getElementById('message');
            let isValid = true;

            // Basic validation example
            if (name.value.trim() === '') {
                isValid = false;
                name.classList.add('border-red-500');
            }

            if (email.value.trim() === '' || !email.value.includes('@')) {
                isValid = false;
                email.classList.add('border-red-500');
            }

            if (message.value.trim() === '') {
                isValid = false;
                message.classList.add('border-red-500');
            }

            if (!isValid) {
                e.preventDefault();
                alert('Please fill out all required fields correctly.');
            }
        });
    </script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/pages/contact-us.blade.php ENDPATH**/ ?>