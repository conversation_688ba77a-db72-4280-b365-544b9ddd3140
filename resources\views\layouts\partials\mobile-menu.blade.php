<!-- Mobile Menu -->
<div :class="{ 'block': open, 'hidden': !open }" class="hidden"
    :class="{ 'bg-white/95 backdrop-blur-sm dark:bg-gray-800/95': isScrolled, 'bg-white dark:bg-gray-800': !isScrolled }">
    <!-- Navigation Links - Mobile -->
    <div class="px-4 py-2 space-y-1">
        <!-- Tools Section -->
        <div x-data="{ open: false }">
            <button @click="open = !open"
                class="w-full flex items-center justify-between px-2 py-2 text-base font-medium text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100">
                <span>Tools</span>
                <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                        clip-rule="evenodd" />
                </svg>
            </button>
            <div x-show="open" class="pl-4">
                @foreach ($allTools as $tool)
                    <x-responsive-nav-link :href="route('tools.show', $tool->slug)">
                        {{ $tool->name }}
                    </x-responsive-nav-link>
                @endforeach
            </div>
        </div>

        <!-- Pincodes Section -->
        <div x-data="{ open: false }">
            <button @click="open = !open"
                class="w-full flex items-center justify-between px-2 py-2 text-base font-medium text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100">
                <span>Pincodes</span>
                <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                        clip-rule="evenodd" />
                </svg>
            </button>
            <div x-show="open" class="pl-4">
                <x-responsive-nav-link :href="route('pincodes.states')">All Pincodes</x-responsive-nav-link>
                <x-responsive-nav-link :href="url('/tools/pincodes.district-wise-pincode-download')">District wise Download</x-responsive-nav-link>
                <x-responsive-nav-link :href="url('/tools/pincode-address-search-tool')">Pincode Address Search</x-responsive-nav-link>
                <x-responsive-nav-link :href="route('courier_dict.index')">Courier Dictionary</x-responsive-nav-link>
                <x-responsive-nav-link :href="route('api.docs.index')">API Documentation</x-responsive-nav-link>
            </div>
        </div>
    </div>

    <!-- Search Form - Mobile -->
    <div class="px-4 py-3">
        <form action="{{ route('search') }}" method="GET" class="w-full">
            <div class="relative mb-3">
                <input type="text" name="query" placeholder="Search pincode or post office..."
                    class="w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 rounded-md shadow-sm">
            </div>
            <div class="flex flex-wrap gap-4 mb-3">
                <label class="inline-flex items-center text-sm text-gray-700 dark:text-gray-300">
                    <input type="radio" name="type" value="pincode" checked
                        class="text-indigo-600 border-gray-300 focus:ring-indigo-500">
                    <span class="ml-1">Pincode</span>
                </label>
                <label class="inline-flex items-center text-sm text-gray-700 dark:text-gray-300">
                    <input type="radio" name="type" value="name"
                        class="text-indigo-600 border-gray-300 focus:ring-indigo-500">
                    <span class="ml-1">Post Office Name</span>
                </label>
            </div>
            <button type="submit"
                class="w-full flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                Search
            </button>
        </form>
    </div>

    <!-- Theme Switcher Button (Mobile) -->
    {{-- <button onclick="toggleTheme()"
            class="relative w-16 h-8 bg-gray-200 dark:bg-gray-700 rounded-full p-1 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
            <div
                class="absolute left-1 top-1 w-6 h-6 bg-yellow-400 dark:bg-blue-500 rounded-full flex items-center justify-center transition-transform duration-300 dark:translate-x-8">
                <span class="text-sm dark:hidden">☀️</span>
                <span class="text-sm hidden dark:inline">🌙</span>
            </div>
            <div class="flex justify-between items-center px-2">
                <span class="text-xs text-gray-600 dark:text-gray-400">☀️</span>
                <span class="text-xs text-gray-600 dark:text-gray-400">🌙</span>
            </div>
        </button> --}}

    <!-- Auth Links - Mobile -->
    @guest
        <div class="px-4 py-3 border-t border-gray-200 dark:border-gray-700">
            <a href="{{ route('login') }}"
                class="w-full flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                Login
            </a>
        </div>
    @else
        <div class="pt-4 pb-3 border-t border-gray-200 dark:border-gray-700">
            <div class="px-4">
                <div class="font-medium text-base text-gray-800 dark:text-gray-200">{{ Auth::user()->name }}</div>
                <div class="font-medium text-sm text-gray-500">{{ Auth::user()->email }}</div>
            </div>

            <div class="mt-3 space-y-1 px-4">
                <x-responsive-nav-link :href="route('profile.edit')">
                    {{ __('Profile') }}
                </x-responsive-nav-link>

                <!-- Authentication -->
                <form method="POST" action="{{ route('logout') }}">
                    @csrf

                    <x-responsive-nav-link :href="route('logout')"
                        onclick="event.preventDefault();
                                    this.closest('form').submit();">
                        {{ __('Log Out') }}
                    </x-responsive-nav-link>
                </form>
            </div>
        </div>
    @endguest
</div>
