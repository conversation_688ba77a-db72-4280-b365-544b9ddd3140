<!-- Mobile Menu -->
<div :class="{
        'block': open,
        'hidden': !open,
        'bg-bg-light/95 backdrop-blur-sm dark:bg-bg-dark/95': isScrolled,
        'bg-bg-light dark:bg-bg-dark': !isScrolled
    }"
    class="hidden md:hidden border-t border-border-light dark:border-border-dark">
    <!-- Navigation Links - Mobile -->
    <div class="px-4 py-4 space-y-2">
        <!-- Tools Section -->
        <div x-data="{ open: false }">
            <button @click="open = !open"
                class="w-full flex items-center justify-between px-3 py-3 text-base font-medium text-text-primary-light dark:text-text-primary-dark hover:text-primary-light dark:hover:text-primary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 rounded-lg transition-all duration-200">
                <span>Tools</span>
                <svg class="h-4 w-4 transition-transform duration-200" :class="{ 'rotate-180': open }" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                        clip-rule="evenodd" />
                </svg>
            </button>
            <div x-show="open" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 transform -translate-y-2" x-transition:enter-end="opacity-100 transform translate-y-0" class="pl-4 space-y-1 mt-2">
                @foreach ($allTools as $tool)
                    <x-responsive-nav-link :href="route('tools.show', $tool->slug)" class="block px-3 py-2 rounded-lg text-sm text-text-secondary-light dark:text-text-secondary-dark hover:text-primary-light dark:hover:text-primary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 transition-all duration-200">
                        <i class="fa-solid fa-tools mr-2 text-xs"></i>
                        {{ $tool->name }}
                    </x-responsive-nav-link>
                @endforeach
            </div>
        </div>

        <!-- Pincodes Section -->
        <div x-data="{ open: false }">
            <button @click="open = !open"
                class="w-full flex items-center justify-between px-3 py-3 text-base font-medium text-text-primary-light dark:text-text-primary-dark hover:text-primary-light dark:hover:text-primary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 rounded-lg transition-all duration-200">
                <span>Pincodes</span>
                <svg class="h-4 w-4 transition-transform duration-200" :class="{ 'rotate-180': open }" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                        clip-rule="evenodd" />
                </svg>
            </button>
            <div x-show="open" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 transform -translate-y-2" x-transition:enter-end="opacity-100 transform translate-y-0" class="pl-4 space-y-1 mt-2">
                <x-responsive-nav-link :href="route('pincodes.states')" class="block px-3 py-2 rounded-lg text-sm text-text-secondary-light dark:text-text-secondary-dark hover:text-primary-light dark:hover:text-primary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 transition-all duration-200">
                    <i class="fa-solid fa-map-pin mr-2 text-xs"></i>
                    All Pincodes
                </x-responsive-nav-link>
                <x-responsive-nav-link :href="url('/tools/pincodes.district-wise-pincode-download')" class="block px-3 py-2 rounded-lg text-sm text-text-secondary-light dark:text-text-secondary-dark hover:text-primary-light dark:hover:text-primary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 transition-all duration-200">
                    <i class="fa-solid fa-download mr-2 text-xs"></i>
                    District wise Download
                </x-responsive-nav-link>
                <x-responsive-nav-link :href="url('/tools/pincode-address-search-tool')" class="block px-3 py-2 rounded-lg text-sm text-text-secondary-light dark:text-text-secondary-dark hover:text-primary-light dark:hover:text-primary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 transition-all duration-200">
                    <i class="fa-solid fa-search mr-2 text-xs"></i>
                    Pincode Address Search
                </x-responsive-nav-link>
                <x-responsive-nav-link :href="route('courier_dict.index')" class="block px-3 py-2 rounded-lg text-sm text-text-secondary-light dark:text-text-secondary-dark hover:text-primary-light dark:hover:text-primary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 transition-all duration-200">
                    <i class="fa-solid fa-truck mr-2 text-xs"></i>
                    Courier Dictionary
                </x-responsive-nav-link>
                <x-responsive-nav-link :href="route('api.docs.index')" class="block px-3 py-2 rounded-lg text-sm text-text-secondary-light dark:text-text-secondary-dark hover:text-primary-light dark:hover:text-primary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 transition-all duration-200">
                    <i class="fa-solid fa-code mr-2 text-xs"></i>
                    API Documentation
                </x-responsive-nav-link>
            </div>
        </div>
    </div>

    <!-- Search Form - Mobile -->
    <div class="px-4 py-4 border-t border-border-light/50 dark:border-border-dark/50">
        <form action="{{ route('search') }}" method="GET" class="w-full">
            <div class="relative mb-4">
                <input type="text" name="query" placeholder="Search pincode or post office..."
                    class="w-full border-border-light dark:border-border-dark bg-bg-light dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark placeholder-text-secondary-light dark:placeholder-text-secondary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark rounded-lg shadow-sm py-3 px-4 transition-all duration-200">
            </div>
            <div class="flex flex-wrap gap-4 mb-4">
                <label class="inline-flex items-center text-sm text-text-secondary-light dark:text-text-secondary-dark">
                    <input type="radio" name="type" value="pincode" checked
                        class="text-primary-light dark:text-primary-dark border-border-light dark:border-border-dark focus:ring-primary-light dark:focus:ring-primary-dark">
                    <span class="ml-2">Pincode</span>
                </label>
                <label class="inline-flex items-center text-sm text-text-secondary-light dark:text-text-secondary-dark">
                    <input type="radio" name="type" value="name"
                        class="text-primary-light dark:text-primary-dark border-border-light dark:border-border-dark focus:ring-primary-light dark:focus:ring-primary-dark">
                    <span class="ml-2">Post Office Name</span>
                </label>
            </div>
            <button type="submit"
                class="w-full flex justify-center items-center px-4 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:focus:ring-primary-dark transition-all duration-200 shadow-lg">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                Search
            </button>
        </form>
    </div>

    <!-- Theme Switcher Button (Mobile) -->
    {{-- <button onclick="toggleTheme()"
            class="relative w-16 h-8 bg-gray-200 dark:bg-gray-700 rounded-full p-1 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
            <div
                class="absolute left-1 top-1 w-6 h-6 bg-yellow-400 dark:bg-blue-500 rounded-full flex items-center justify-center transition-transform duration-300 dark:translate-x-8">
                <span class="text-sm dark:hidden">☀️</span>
                <span class="text-sm hidden dark:inline">🌙</span>
            </div>
            <div class="flex justify-between items-center px-2">
                <span class="text-xs text-gray-600 dark:text-gray-400">☀️</span>
                <span class="text-xs text-gray-600 dark:text-gray-400">🌙</span>
            </div>
        </button> --}}

    <!-- Auth Links - Mobile -->
    @guest
        <div class="px-4 py-4 border-t border-border-light/50 dark:border-border-dark/50">
            <a href="{{ route('login') }}"
                class="w-full flex justify-center items-center px-4 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-accent-light hover:bg-accent-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent-light transition-all duration-200 shadow-lg">
                <i class="fa-solid fa-sign-in-alt mr-2"></i>
                Login
            </a>
        </div>
    @else
        <div class="pt-4 pb-3 border-t border-border-light/50 dark:border-border-dark/50">
            <div class="px-4 mb-3">
                <div class="font-medium text-base text-text-primary-light dark:text-text-primary-dark">{{ Auth::user()->name }}</div>
                <div class="font-medium text-sm text-text-secondary-light dark:text-text-secondary-dark">{{ Auth::user()->email }}</div>
            </div>

            <div class="space-y-1 px-4">
                <x-responsive-nav-link :href="route('profile.edit')" class="flex items-center px-3 py-2 rounded-lg text-text-secondary-light dark:text-text-secondary-dark hover:text-primary-light dark:hover:text-primary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 transition-all duration-200">
                    <i class="fa-solid fa-user mr-2"></i>
                    {{ __('Profile') }}
                </x-responsive-nav-link>

                <!-- Authentication -->
                <form method="POST" action="{{ route('logout') }}">
                    @csrf

                    <x-responsive-nav-link :href="route('logout')"
                        onclick="event.preventDefault(); this.closest('form').submit();"
                        class="flex items-center px-3 py-2 rounded-lg text-text-secondary-light dark:text-text-secondary-dark hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-200">
                        <i class="fa-solid fa-sign-out-alt mr-2"></i>
                        {{ __('Log Out') }}
                    </x-responsive-nav-link>
                </form>
            </div>
        </div>
    @endguest
</div>
