<?php
    $schema = [
        "@context" => "https://schema.org",
        "@type" => "Organization",
        "name" => get_setting('schema_organization_name', 'PinCode Directory'),
        "url" => get_setting('schema_organization_url', url('/')),
        "logo" =>  uploads_url(get_setting('site_logo')),
        "contactPoint" => [
            "@type" => "ContactPoint",
            "contactType" => 'Customer Service',
            "telephone" => get_setting('contact_phone'),
            "email" => get_setting('contact_email')
        ],
        "sameAs" => [
            get_setting('facebook_link'),
            get_setting('twitter_link'),
            get_setting('linkedin_link'),
            get_setting('instagram_link'),
            get_setting('youtube_link')
        ]
    ];
?>

<script type="application/ld+json">
    <?php echo json_encode($schema, JSON_PRETTY_PRINT); ?>

</script>
<?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/pincodes/json-ld/organization.blade.php ENDPATH**/ ?>