<?php $__env->startSection('content'); ?>
    <div class="container max-w-6xl mx-auto px-4 py-8">
        <!-- Breadcrumbs -->
        <?php if (isset($component)) { $__componentOriginal269900abaed345884ce342681cdc99f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal269900abaed345884ce342681cdc99f6 = $attributes; } ?>
<?php $component = App\View\Components\Breadcrumb::resolve(['segments' => $breadcrumbs,'pageTitle' => $pageTitle] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Breadcrumb::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal269900abaed345884ce342681cdc99f6)): ?>
<?php $attributes = $__attributesOriginal269900abaed345884ce342681cdc99f6; ?>
<?php unset($__attributesOriginal269900abaed345884ce342681cdc99f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal269900abaed345884ce342681cdc99f6)): ?>
<?php $component = $__componentOriginal269900abaed345884ce342681cdc99f6; ?>
<?php unset($__componentOriginal269900abaed345884ce342681cdc99f6); ?>
<?php endif; ?>

        <div class="bg-white dark:bg-bg-dark rounded-lg shadow-lg p-6">
            <!-- Header -->
            <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
                <div>
                    <h1 class="text-3xl font-bold text-text-primary-light dark:text-text-primary-dark mb-2">
                        <?php echo e($pageTitle); ?></h1>
                    <p class="text-text-secondary-light dark:text-text-secondary-dark">
                        <?php echo e($metaDescription ?? "All reviews for {$tool->name}"); ?></p>
                </div>
                <div class="mt-4 md:mt-0">
                    <a href="<?php echo e(route('tools.show', $tool->slug)); ?>"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-light dark:bg-primary-dark hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:focus:ring-primary-dark transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                        </svg>
                        Back to Tool
                    </a>
                </div>
            </div>

            <!-- Reviews Stats -->
            <?php if($reviews->total() > 0): ?>
                <div
                    class="bg-bg-light dark:bg-bg-dark/50 border border-border-light dark:border-border-dark rounded-lg p-4 mb-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark">
                                <?php echo e($reviews->total()); ?> <?php echo e(Str::plural('Review', $reviews->total())); ?>

                            </p>
                            <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                Showing <?php echo e($reviews->firstItem()); ?> to <?php echo e($reviews->lastItem()); ?> of
                                <?php echo e($reviews->total()); ?> reviews
                            </p>
                        </div>
                        <?php
                            $averageRating = $reviews->avg('rating');
                            $ratedReviewsCount = $reviews->where('rating', '!=', null)->count();
                        ?>
                        <?php if($averageRating && $ratedReviewsCount > 0): ?>
                            <div class="text-right">
                                <div class="flex items-center justify-end mb-1">
                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                        <svg class="w-5 h-5 <?php echo e($i <= round($averageRating) ? 'text-accent-light dark:text-accent-dark' : 'text-text-secondary-light/40 dark:text-text-secondary-dark/40'); ?>"
                                            fill="currentColor" viewBox="0 0 20 20">
                                            <path
                                                d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                    <?php endfor; ?>
                                </div>
                                <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                    <?php echo e(number_format($averageRating, 1)); ?>/5 (<?php echo e($ratedReviewsCount); ?>

                                    <?php echo e(Str::plural('rating', $ratedReviewsCount)); ?>)
                                </p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Reviews List -->
            <?php if($reviews->count() > 0): ?>
                <div id="reviewsList" class="space-y-6">
                    <?php $__currentLoopData = $reviews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $review): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="border-b border-border-light dark:border-border-dark pb-6 last:border-b-0 last:pb-0">
                            <div class="flex items-start justify-between mb-3">
                                <div class="flex-1">
                                    <div class="flex items-center mb-2">
                                        <div
                                            class="w-10 h-10 bg-primary-light/10 dark:bg-primary-dark/10 rounded-full flex items-center justify-center mr-3">
                                            <span class="text-primary-light dark:text-primary-dark font-semibold text-sm">
                                                <?php echo e(strtoupper(substr($review->user ? $review->user->name : $review->name, 0, 1))); ?>

                                            </span>
                                        </div>
                                        <div>
                                            <h3
                                                class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark">
                                                <?php echo e($review->user ? $review->user->name : $review->name); ?>

                                            </h3>
                                            <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                                <?php echo e($review->created_at->format('F j, Y \a\t g:i A')); ?>

                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <?php if($review->rating): ?>
                                    <div class="flex items-center ml-4">
                                        <div class="flex items-center">
                                            <?php for($i = 1; $i <= 5; $i++): ?>
                                                <svg class="w-5 h-5 <?php echo e($i <= $review->rating ? 'text-accent-light dark:text-accent-dark' : 'text-text-secondary-light/40 dark:text-text-secondary-dark/40'); ?>"
                                                    fill="currentColor" viewBox="0 0 20 20">
                                                    <path
                                                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                </svg>
                                            <?php endfor; ?>
                                        </div>
                                        <span
                                            class="ml-2 text-sm text-text-secondary-light dark:text-text-secondary-dark"><?php echo e($review->rating); ?>/5</span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div
                                class="prose max-w-none text-text-primary-light dark:text-text-primary-dark leading-relaxed">
                                <?php echo e($review->review); ?>

                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Load More Button -->
                <?php if($reviews->hasPages()): ?>
                    <div class="mt-8 text-center">
                        <button id="loadMoreBtn" data-next-page="<?php echo e($reviews->nextPageUrl()); ?>"
                            class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary-light dark:bg-primary-dark hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:focus:ring-primary-dark transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                            <svg id="loadMoreIcon" class="w-5 h-5 mr-2" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            <span id="loadMoreText">Load More Reviews</span>
                        </button>

                        <!-- Loading indicator -->
                        <div id="loadingIndicator" class="hidden mt-4">
                            <div class="flex items-center justify-center">
                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-primary-light dark:text-primary-dark"
                                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                        stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor"
                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                    </path>
                                </svg>
                                <span class="text-text-secondary-light dark:text-text-secondary-dark">Loading more
                                    reviews...</span>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-text-secondary-light dark:text-text-secondary-dark" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-text-primary-light dark:text-text-primary-dark">No reviews yet
                    </h3>
                    <p class="mt-1 text-sm text-text-secondary-light dark:text-text-secondary-dark">Be the first to review
                        this tool!</p>
                    <div class="mt-6">
                        <a href="<?php echo e(route('tools.show', $tool->slug)); ?>"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-light dark:bg-primary-dark hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:focus:ring-primary-dark">
                            Go to Tool
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <!-- JavaScript for Load More functionality -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loadMoreBtn = document.getElementById('loadMoreBtn');
            const reviewsList = document.getElementById('reviewsList');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const loadMoreIcon = document.getElementById('loadMoreIcon');
            const loadMoreText = document.getElementById('loadMoreText');

            if (loadMoreBtn) {
                loadMoreBtn.addEventListener('click', function() {
                    const nextPageUrl = this.getAttribute('data-next-page');

                    if (!nextPageUrl) return;

                    // Show loading state
                    loadMoreBtn.disabled = true;
                    loadingIndicator.classList.remove('hidden');
                    loadMoreIcon.classList.add('animate-spin');
                    loadMoreText.textContent = 'Loading...';

                    // Fetch next page
                    fetch(nextPageUrl, {
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest',
                                'Accept': 'application/json'
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.html) {
                                // Append new reviews to the list
                                reviewsList.insertAdjacentHTML('beforeend', data.html);

                                // Update next page URL
                                if (data.next_page_url) {
                                    loadMoreBtn.setAttribute('data-next-page', data.next_page_url);
                                } else {
                                    // No more pages, hide the button
                                    loadMoreBtn.style.display = 'none';
                                }
                            }
                        })
                        .catch(error => {
                            console.error('Error loading more reviews:', error);
                            alert('Error loading more reviews. Please try again.');
                        })
                        .finally(() => {
                            // Reset loading state
                            loadMoreBtn.disabled = false;
                            loadingIndicator.classList.add('hidden');
                            loadMoreIcon.classList.remove('animate-spin');
                            loadMoreText.textContent = 'Load More Reviews';
                        });
                });
            }
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/tools/tool-reviews.blade.php ENDPATH**/ ?>