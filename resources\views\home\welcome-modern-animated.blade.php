@extends('layouts.app')

@section('content')
    <!-- Scroll Progress Indicator -->
    <div class="scroll-indicator" id="scrollIndicator"></div>
    <!-- Interactive Cursor -->
    <div class="interactive-cursor" id="cursor"></div>
    <!-- Partners Marquee Section -->
    <div id="partners-marquee">
    <div class="partners-track">
    <!-- Content for the partners track goes here -->
    </div>
    </div>
    
    <!-- Animated Hero Section -->
    <section id="hero-animated"
        class="relative overflow-hidden h-auto min-h-screen sm:h-screen flex items-start sm:items-center justify-center bg-bg-light dark:bg-bg-dark transition-colors duration-300 pt-16 sm:pt-0">
        <!-- Enhanced Parallax, Morphing Blobs, and Interactive BG -->
        <div class="absolute inset-0 z-0 pointer-events-none">
            <div class="parallax-bg" id="parallaxBg">
                <!-- Enhanced floating elements with better animations -->
                <div class="floating-elements">
                    <div class="floating-element text-6xl animate-bounce-slow" style="animation-delay: 0s;">📍</div>
                    <div class="floating-element text-5xl animate-pulse-slow" style="left: 15%; animation-delay: -2s;">🏢</div>
                    <div class="floating-element text-4xl animate-wiggle" style="left: 35%; animation-delay: -4s;">📮</div>
                    <div class="floating-element text-6xl animate-spin-slow" style="left: 65%; animation-delay: -6s;">🌏</div>
                    <div class="floating-element text-5xl animate-float-gentle" style="left: 85%; animation-delay: -8s;">📊</div>
                    <div class="floating-element text-3xl animate-bounce-slow" style="left: 25%; top: 70%; animation-delay: -10s;">🚚</div>
                    <div class="floating-element text-4xl animate-pulse-slow" style="left: 75%; top: 20%; animation-delay: -12s;">📬</div>
                </div>
                <!-- Enhanced morphing blobs with theme colors -->
                <div class="morphing-blob enhanced-blob w-96 h-96 top-10 left-10 bg-primary-light/30 dark:bg-primary-dark/30"
                    style="animation-delay: 0s;"></div>
                <div class="morphing-blob enhanced-blob w-80 h-80 top-1/2 right-10 bg-accent-light/25 dark:bg-accent-dark/25"
                    style="animation-delay: -2s;"></div>
                <div class="morphing-blob enhanced-blob w-72 h-72 bottom-10 left-1/3 bg-primary-dark/20 dark:bg-primary-light/20"
                    style="animation-delay: -4s;"></div>
                <!-- Additional smaller blobs for more dynamic effect -->
                <div class="morphing-blob enhanced-blob w-48 h-48 top-1/4 left-1/2 bg-accent-dark/15 dark:bg-accent-light/15"
                    style="animation-delay: -6s;"></div>
                <div class="morphing-blob enhanced-blob w-64 h-64 bottom-1/4 right-1/4 bg-primary-light/20 dark:bg-primary-dark/20"
                    style="animation-delay: -8s;"></div>
            </div>
            <div class="absolute inset-0 interactive-bg dark:bg-bg-dark/80" id="interactiveBg"></div>
            <!-- Particle system -->
            <div class="absolute inset-0 particle-system" id="particleSystem"></div>
        </div>
        <div
            class="relative z-10 w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex flex-col md:flex-row items-start md:items-center justify-center gap-12">
            <div
                class="flex-1 hero-texts flex flex-col items-center md:items-start justify-center text-center md:text-left">
                <span
                    class="inline-block px-6 py-2 rounded-full text-xs font-bold tracking-wider uppercase mb-6 hero-badge glass-effect enhanced-glow bg-gradient-to-r from-primary-light to-accent-light dark:from-primary-dark dark:to-accent-dark text-white shadow-lg animate-pulse-glow">
                    <i class="fa-solid fa-trophy mr-2"></i>India's #1 Postal Directory
                </span>
                <h1
                    class="text-5xl md:text-7xl font-black tracking-tight mb-8 leading-tight hero-title">
                    {!! $landingPage['hero']['content']['heading'] ??
                        '<span class="block mb-2 text-text-primary-light dark:text-text-primary-dark animate-slide-in-left">Discover</span><span class="enhanced-gradient-text typing-animation block animate-slide-in-right">Every Pincode</span><span class="block text-text-primary-light dark:text-text-primary-dark animate-slide-in-left">in India</span>' !!}
                </h1>
                <p
                    class="text-xl md:text-2xl text-text-secondary-light dark:text-text-secondary-dark mb-10 max-w-2xl hero-subtitle mx-auto md:mx-0 leading-relaxed animate-fade-in-up">
                    {{ $landingPage['hero']['content']['subheading'] ?? 'Instantly search, verify, and explore 155,000+ Indian pincodes with rich, verified data and interactive tools.' }}
                </p>
                <div class="flex flex-col sm:flex-row gap-6 hero-ctas justify-center md:justify-start">
                    <a href="{{ $landingPage['hero']['content']['cta_link'] ?? '#search-section' }}"
                        class="group px-10 py-4 text-white rounded-xl shadow-2xl transition-all duration-500 text-xl font-bold flex items-center gap-3 enhanced-glow bg-gradient-to-r from-primary-light to-primary-dark hover:from-primary-dark hover:to-accent-light transform hover:scale-105 hover:-translate-y-1">
                        <span>{{ $landingPage['hero']['content']['cta_text'] ?? 'Start Searching' }}</span>
                        <i class="fa-solid fa-magnifying-glass group-hover:rotate-12 transition-transform duration-300"></i>
                    </a>
                    <a href="#features"
                        class="group px-10 py-4 border-2 rounded-xl shadow-xl transition-all duration-500 text-xl font-bold flex items-center gap-3 glass-effect bg-white/10 dark:bg-bg-dark/10 backdrop-blur-md text-primary-light dark:text-primary-dark border-primary-light/30 dark:border-primary-dark/30 hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 transform hover:scale-105">
                        <span>See Features</span>
                        <i class="fa-solid fa-star group-hover:rotate-180 transition-transform duration-500"></i>
                    </a>
                </div>
            </div>
            <div class="flex-1 flex justify-center items-center min-h-[400px]">
                <div class="relative w-full max-w-lg">
                    <!-- Enhanced image container with multiple animations -->
                    <div class="relative animate-float-complex">
                        <img src="{{ uploads_url('images/india-map.webp') }}" alt="India Map"
                            class="rounded-3xl shadow-2xl border-4 border-gradient-to-r from-primary-light to-accent-light dark:from-primary-dark dark:to-accent-dark bg-white dark:bg-bg-dark transform transition-all duration-700 hover:scale-105 hover:rotate-1" />
                        <!-- Animated border glow -->
                        <div class="absolute inset-0 rounded-3xl border-4 border-gradient-to-r from-primary-light/50 to-accent-light/50 dark:from-primary-dark/50 dark:to-accent-dark/50 animate-pulse-border"></div>
                    </div>
                    <!-- Enhanced verification badge -->
                    <div
                        class="absolute bottom-6 right-6 rounded-2xl shadow-2xl p-5 flex items-center gap-3 animate-slide-in-bottom bg-white/95 dark:bg-bg-dark/95 backdrop-blur-md border border-border-light/50 dark:border-border-dark/50">
                        <i class="fa-solid fa-circle-check text-green-500 text-2xl animate-bounce-gentle"></i>
                        <div>
                            <span class="font-bold text-text-primary-light dark:text-text-primary-dark block">Verified by</span>
                            <span class="font-semibold text-primary-light dark:text-primary-dark">India Post</span>
                        </div>
                    </div>
                    <!-- Floating stats badges -->
                    <div class="absolute -top-4 -left-4 bg-gradient-to-r from-accent-light to-accent-dark text-white px-4 py-2 rounded-full shadow-lg animate-bounce-slow">
                        <span class="font-bold text-sm">155K+ Pincodes</span>
                    </div>
                    <div class="absolute -bottom-4 -left-8 bg-gradient-to-r from-primary-light to-primary-dark text-white px-4 py-2 rounded-full shadow-lg animate-bounce-slow" style="animation-delay: -1s;">
                        <span class="font-bold text-sm">36 States</span>
                    </div>
                </div>
            </div>
        </div>
    </section>


    <section id="search-section"
        class="py-20 bg-gradient-to-br from-primary-light/10 via-accent-light/5 to-primary-dark/10 dark:from-primary-dark/10 dark:via-accent-dark/5 dark:to-primary-light/10 transition-colors duration-300 relative overflow-hidden">
        <!-- Animated background elements -->
        <div class="absolute inset-0 pointer-events-none">
            <div class="absolute top-10 left-10 w-32 h-32 bg-primary-light/10 dark:bg-primary-dark/10 rounded-full animate-pulse-slow"></div>
            <div class="absolute bottom-10 right-10 w-24 h-24 bg-accent-light/10 dark:bg-accent-dark/10 rounded-full animate-bounce-slow"></div>
            <div class="absolute top-1/2 left-1/4 w-16 h-16 bg-primary-dark/10 dark:bg-primary-light/10 rounded-full animate-float-gentle"></div>
        </div>

        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="max-w-3xl mx-auto">
                <div class="text-center mb-12 animate-fade-in-up">
                    <span class="inline-block px-4 py-2 rounded-full bg-gradient-to-r from-primary-light/20 to-accent-light/20 dark:from-primary-dark/20 dark:to-accent-dark/20 text-primary-light dark:text-primary-dark text-sm font-semibold tracking-wider uppercase mb-4 backdrop-blur-sm border border-primary-light/30 dark:border-primary-dark/30">
                        <i class="fa-solid fa-search mr-2"></i>Search Tool
                    </span>
                    <h2 class="text-4xl md:text-5xl font-black mb-6 text-primary-light dark:text-primary-dark">
                        Find Any Pincode In India
                    </h2>
                    <p class="text-xl text-text-secondary-light dark:text-text-secondary-dark leading-relaxed">
                        Enter a pincode, locality, district, or state to find detailed information
                    </p>
                </div>

                <div
                    class="bg-white/80 dark:bg-bg-dark/80 backdrop-blur-xl rounded-2xl shadow-2xl p-8 md:p-10 border border-border-light/50 dark:border-border-dark/50 transition-all duration-500 hover:shadow-3xl hover:scale-[1.02] animate-slide-in-bottom">
                    <form id="searchForm" class="relative">
                        <div class="flex flex-col md:flex-row gap-4">
                            <div class="flex-1">
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <svg class="h-5 w-5 text-text-secondary-light dark:text-text-secondary-dark"
                                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                            stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                    </div>
                                    <input type="text" name="query" id="search"
                                        class="block w-full pl-10 pr-3 py-4 border border-border-light dark:border-border-dark rounded-lg bg-bg-light dark:bg-bg-dark focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark transition-all duration-200 placeholder-text-secondary-light dark:placeholder-text-secondary-dark text-text-primary-light dark:text-text-primary-dark"
                                        placeholder="Enter pincode or locality name">
                                </div>
                            </div>
                            <div>
                                <button type="submit"
                                    class="bg-primary-light hover:bg-primary-light/90 dark:bg-primary-dark dark:hover:bg-primary-dark/90 text-white w-full md:w-auto py-4 px-8 rounded-lg font-medium flex items-center justify-center transition-all duration-200 shadow-md hover:shadow-lg">
                                    <span>Search</span>
                                    <svg class="ml-2 w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <div class="mt-6 flex items-center justify-center md:justify-start gap-6">
                            <div class="flex items-center">
                                <input type="radio" id="type-pincode" name="type" value="pincode" checked=""
                                    class="h-4 w-4 text-primary-light dark:text-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark border-border-light dark:border-border-dark bg-bg-light dark:bg-bg-dark">
                                <label for="type-pincode"
                                    class="ml-2 text-sm text-text-secondary-light dark:text-text-secondary-dark font-medium">Pincode</label>
                            </div>
                            <div class="flex items-center">
                                <input type="radio" id="type-name" name="type" value="name"
                                    class="h-4 w-4 text-primary-light dark:text-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark border-border-light dark:border-border-dark bg-bg-light dark:bg-bg-dark">
                                <label for="type-name"
                                    class="ml-2 text-sm text-text-secondary-light dark:text-text-secondary-dark font-medium">Post
                                    Office</label>
                            </div>
                            <div class="flex items-center">
                                <input type="radio" id="type-district" name="type" value="district"
                                    class="h-4 w-4 text-primary-light dark:text-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark border-border-light dark:border-border-dark bg-bg-light dark:bg-bg-dark">
                                <label for="type-district"
                                    class="ml-2 text-sm text-text-secondary-light dark:text-text-secondary-dark font-medium">District</label>
                            </div>
                        </div>
                    </form>

                    <div class="mt-8 pt-6 border-t border-border-light dark:border-border-dark">
                        <div class="text-center">
                            <h3
                                class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark uppercase tracking-wider mb-3">
                                Popular Searches
                            </h3>
                            <div class="flex flex-wrap justify-center gap-2">
                                <a href="/search?query=delhi&amp;type=name"
                                    class="px-3 py-1 bg-bg-light dark:bg-bg-dark hover:bg-accent-light/10 dark:hover:bg-accent-dark/10 hover:text-accent-light dark:hover:text-accent-dark rounded-full text-sm text-text-secondary-light dark:text-text-secondary-dark transition-all duration-200 border border-border-light dark:border-border-dark hover:border-accent-light dark:hover:border-accent-dark">Delhi</a>
                                <a href="/search?query=mumbai&amp;type=name"
                                    class="px-3 py-1 bg-bg-light dark:bg-bg-dark hover:bg-accent-light/10 dark:hover:bg-accent-dark/10 hover:text-accent-light dark:hover:text-accent-dark rounded-full text-sm text-text-secondary-light dark:text-text-secondary-dark transition-all duration-200 border border-border-light dark:border-border-dark hover:border-accent-light dark:hover:border-accent-dark">Mumbai</a>
                                <a href="/search?query=bangalore&amp;type=name"
                                    class="px-3 py-1 bg-bg-light dark:bg-bg-dark hover:bg-accent-light/10 dark:hover:bg-accent-dark/10 hover:text-accent-light dark:hover:text-accent-dark rounded-full text-sm text-text-secondary-light dark:text-text-secondary-dark transition-all duration-200 border border-border-light dark:border-border-dark hover:border-accent-light dark:hover:border-accent-dark">Bangalore</a>
                                <a href="/search?query=hyderabad&amp;type=name"
                                    class="px-3 py-1 bg-bg-light dark:bg-bg-dark hover:bg-accent-light/10 dark:hover:bg-accent-dark/10 hover:text-accent-light dark:hover:text-accent-dark rounded-full text-sm text-text-secondary-light dark:text-text-secondary-dark transition-all duration-200 border border-border-light dark:border-border-dark hover:border-accent-light dark:hover:border-accent-dark">Hyderabad</a>
                                <a href="/search?query=chennai&amp;type=name"
                                    class="px-3 py-1 bg-bg-light dark:bg-bg-dark hover:bg-accent-light/10 dark:hover:bg-accent-dark/10 hover:text-accent-light dark:hover:text-accent-dark rounded-full text-sm text-text-secondary-light dark:text-text-secondary-dark transition-all duration-200 border border-border-light dark:border-border-dark hover:border-accent-light dark:hover:border-accent-dark">Chennai</a>
                                <a href="/search?query=kolkata&amp;type=name"
                                    class="px-3 py-1 bg-bg-light dark:bg-bg-dark hover:bg-accent-light/10 dark:hover:bg-accent-dark/10 hover:text-accent-light dark:hover:text-accent-dark rounded-full text-sm text-text-secondary-light dark:text-text-secondary-dark transition-all duration-200 border border-border-light dark:border-border-dark hover:border-accent-light dark:hover:border-accent-dark">Kolkata</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    @if (isset($landingPage['features']))
        <!-- Features Section -->
        <section id="features"
            class="py-20 bg-gradient-to-br from-primary-light/10 to-accent-light/10 dark:from-primary-dark/10 dark:to-accent-dark/10 transition-colors duration-300">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div
                    class="text-center max-w-3xl mx-auto mb-16 dark:from-accent-dark to-accent-light rounded-2xl p-8 shadow-lg">
                    <span
                        class="inline-block px-3 py-1 rounded-full bg-primary-light/20 text-primary-light dark:bg-primary-dark/20 dark:text-primary-dark text-xs font-semibold tracking-wider uppercase mb-3">Features</span>
                    <h2
                        class="text-3xl md:text-4xl font-extrabold mb-4 text-text-primary-light dark:text-text-primary-dark">
                        {{ $landingPage['features']['content']['heading'] ?? 'Everything you need for postal code information' }}
                    </h2>
                    <p class="text-lg text-text-secondary-light dark:text-text-secondary-dark">Our comprehensive pincode
                        directory provides accurate and up-to-date information for all your needs.</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
                    @foreach ($landingPage['features']['content']['features'] ?? [] as $feature)
                        <div
                            class="bg-white dark:bg-bg-dark rounded-xl shadow-lg p-6 card-hover transition-colors duration-300 border border-border-light dark:border-border-dark">
                            <div
                                class="w-12 h-12 bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark rounded-xl flex items-center justify-center mb-5">
                                {!! $feature['icon'] ?? '' !!}
                            </div>
                            <h3 class="text-xl font-bold mb-3 text-text-primary-light dark:text-text-primary-dark">
                                {{ $feature['title'] ?? '' }}</h3>
                            <p class="text-text-secondary-light dark:text-text-secondary-dark mb-4">
                                {{ $feature['description'] ?? '' }}</p>
                            <a href="#"
                                class="text-primary-light dark:text-primary-dark hover:text-accent-light dark:hover:text-accent-dark font-medium inline-flex items-center transition-colors duration-200">
                                Learn more
                                <svg class="w-4 h-4 ml-1" xmlns="http://www.w3.org/2000/svg" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7" />
                                </svg>
                            </a>
                        </div>
                    @endforeach
                </div>
            </div>
        </section>
    @endif

    <!-- Stats Section -->

    @if (isset($landingPage['stats']))
        <section id="stats" class="py-20 bg-bg-light dark:bg-bg-dark transition-colors duration-300">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center max-w-3xl mx-auto mb-16">
                    <span
                        class="inline-block px-3 py-1 rounded-full bg-primary-light text-white dark:bg-primary-dark dark:text-white text-xs font-semibold tracking-wider uppercase mb-3">Coverage</span>
                    <h2
                        class="text-3xl md:text-4xl font-extrabold mb-4 text-text-primary-light dark:text-text-primary-dark">
                        {{ $landingPage['stats']['content']['heading'] ?? 'Complete Coverage Across India' }}
                    </h2>
                    <p class="text-lg text-text-secondary-light dark:text-text-secondary-dark">Our comprehensive
                        database covers postal codes across all states and districts</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div
                        class="card p-6 text-center card-hover bg-white dark:bg-bg-dark rounded-xl shadow-lg transition-colors duration-300 border border-border-light dark:border-border-dark">
                        <div
                            class="w-16 h-16 mx-auto bg-primary-light text-white rounded-full flex items-center justify-center mb-4 dark:bg-primary-dark dark:text-white">
                            <svg class="w-8 h-8" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <span
                            class="text-4xl font-extrabold text-primary-light dark:text-primary-dark">{{ $landingPage['stats']['content']['states_count'] ?? '36' }}</span>
                        <p class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark mt-2">States
                            & Union Territories</p>
                        <p class="text-text-secondary-light mt-2 dark:text-text-secondary-dark">Complete coverage
                            across all states and union territories in India</p>
                    </div>

                    <div
                        class="card p-6 text-center card-hover transform translate-y-4 bg-white dark:bg-bg-dark rounded-xl shadow-lg transition-colors duration-300 border border-border-light dark:border-border-dark">
                        <div
                            class="w-16 h-16 mx-auto bg-primary-light text-white rounded-full flex items-center justify-center mb-4 dark:bg-primary-dark dark:text-white">
                            <svg class="w-8 h-8" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                        </div>
                        <span
                            class="text-4xl font-extrabold text-primary-light dark:text-primary-dark">{{ $landingPage['stats']['content']['districts_count'] ?? '700+' }}</span>
                        <p class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark mt-2">
                            Districts Mapped</p>
                        <p class="text-text-secondary-light mt-2 dark:text-text-secondary-dark">Detailed mapping of all
                            districts with their corresponding pincodes</p>
                    </div>

                    <div
                        class="card p-6 text-center card-hover bg-white dark:bg-bg-dark rounded-xl shadow-lg transition-colors duration-300 border border-border-light dark:border-border-dark">
                        <div
                            class="w-16 h-16 mx-auto bg-primary-light text-white rounded-full flex items-center justify-center mb-4 dark:bg-primary-dark dark:text-white">
                            <svg class="w-8 h-8" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                        </div>
                        <span
                            class="text-4xl font-extrabold text-primary-light dark:text-primary-dark">{{ $landingPage['stats']['content']['delivery_offices_count'] ?? '155,000+' }}</span>
                        <p class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark mt-2">
                            Delivery Offices</p>
                        <p class="text-text-secondary-light mt-2 dark:text-text-secondary-dark">Information on post
                            offices and their delivery status across India</p>
                    </div>
                </div>
            </div>
        </section>
    @endif

    <!-- Tools Section -->
    @if (isset($landingPage['tools']))
        <section id="tools"
            class="py-20 bg-gradient-to-b from-bg-light to-white dark:from-bg-dark dark:to-gray-800 transition-colors duration-300">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center max-w-3xl mx-auto mb-16">
                    <span
                        class="inline-block px-3 py-1 rounded-full bg-primary-light text-white dark:bg-primary-dark dark:text-white text-xs font-semibold tracking-wider uppercase mb-3">Tools</span>
                    <h2
                        class="text-3xl md:text-4xl font-extrabold mb-4 text-text-primary-light dark:text-text-primary-dark">
                        Quick Pincode Tools</h2>
                    <p class="text-lg text-text-secondary-light dark:text-text-secondary-dark">Access our most popular
                        pincode search and verification tools</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <!-- Tool 1: Browse by State -->
                    <div
                        class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 card-hover border border-border-light dark:border-border-dark">
                        <div
                            class="h-3 bg-gradient-to-r from-primary-light to-accent-light dark:from-primary-dark dark:to-accent-dark">
                        </div>
                        <div class="p-6">
                            <div
                                class="flex items-center justify-center h-12 w-12 rounded-full bg-primary-light/10 dark:bg-primary-dark/20 text-primary-light dark:text-primary-dark mb-5">
                                <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold text-text-primary-light dark:text-text-primary-dark mb-2">
                                Browse by State</h3>
                            <p class="text-text-secondary-light dark:text-text-secondary-dark mb-6">Explore pincodes
                                organized by states and districts across India. Navigate through our hierarchical
                                directory.</p>
                            <a href="/pincodes"
                                class="inline-flex items-center text-primary-light dark:text-primary-dark hover:text-primary-light/80 dark:hover:text-primary-dark/80 transition-colors">
                                View States
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" viewBox="0 0 20 20"
                                    fill="currentColor">
                                    <path fill-rule="evenodd"
                                        d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                                        clip-rule="evenodd" />
                                </svg>
                            </a>
                        </div>
                    </div>

                    <!-- Tool 2: All Pincodes List -->
                    <div
                        class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 card-hover border border-border-light dark:border-border-dark">
                        <div class="h-3 bg-accent-light dark:bg-accent-dark"></div>
                        <div class="p-6">
                            <div
                                class="flex items-center justify-center h-12 w-12 rounded-full bg-accent-light/10 dark:bg-accent-dark/20 text-accent-light dark:text-accent-dark mb-5">
                                <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold text-text-primary-light dark:text-text-primary-dark mb-2">
                                Complete Pincode List</h3>
                            <p class="text-text-secondary-light dark:text-text-secondary-dark mb-6">Access the
                                comprehensive list of all postal codes in India. Search, filter, and find any pincode
                                instantly.</p>
                            <a href="/india-postal-code-list"
                                class="inline-flex items-center text-accent-light dark:text-accent-dark hover:text-accent-light/80 dark:hover:text-accent-dark/80 transition-colors">
                                View All Pincodes
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" viewBox="0 0 20 20"
                                    fill="currentColor">
                                    <path fill-rule="evenodd"
                                        d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                                        clip-rule="evenodd" />
                                </svg>
                            </a>
                        </div>
                    </div>

                    <!-- Tool 3: Download Pincodes -->
                    <div
                        class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 card-hover border border-border-light dark:border-border-dark">
                        <div
                            class="h-3 bg-gradient-to-r from-accent-light to-primary-light dark:from-accent-dark dark:to-primary-dark">
                        </div>

                        <div class="p-6">
                            <div
                                class="flex items-center justify-center h-12 w-12 rounded-full bg-gradient-to-r from-primary-light/10 to-accent-light/10 dark:from-primary-dark/20 dark:to-accent-dark/20 text-primary-light dark:text-primary-dark mb-5">
                                <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold text-text-primary-light dark:text-text-primary-dark mb-2">
                                Download Pincodes</h3>
                            <p class="text-text-secondary-light dark:text-text-secondary-dark mb-6">Download pincode
                                data for your business or personal use. Available in multiple formats for easy
                                integration.</p>
                            <a href="/tools/district-wise-pincode-download"
                                class="inline-flex items-center text-primary-light dark:text-primary-dark hover:text-primary-light/80 dark:hover:text-primary-dark/80 transition-colors">
                                Download Options
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" viewBox="0 0 20 20"
                                    fill="currentColor">
                                    <path fill-rule="evenodd"
                                        d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                                        clip-rule="evenodd" />
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    @endif


    @php
        $testimonialsData = $landingPage['testimonials'] ?? null;
        $heading = $testimonialsData['content']['heading'] ?? 'What our users say';
        $subheading = $testimonialsData['content']['subheading'] ?? '';
        $testimonials = $testimonialsData['content']['testimonials'] ?? [];
    @endphp

    @if ($testimonialsData && $testimonialsData['active'] && !empty($testimonials))
        <section id="testimonials" class="py-20 bg-bg-light dark:bg-bg-dark transition-colors duration-300">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <!-- Section Header -->
                <div class="text-center mb-16">
                    <h2 class="text-3xl md:text-4xl font-bold text-text-primary-light dark:text-text-primary-dark mb-4">
                        {{ $heading }}
                    </h2>
                    @if ($subheading)
                        <p class="text-lg text-text-secondary-light dark:text-text-secondary-dark max-w-3xl mx-auto">
                            {{ $subheading }}
                        </p>
                    @endif
                </div>

                <!-- Testimonials Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    @foreach ($testimonials as $testimonial)
                        <div
                            class="bg-white dark:bg-bg-dark rounded-xl shadow-md hover:shadow-lg transition-shadow duration-300 p-6 border border-border-light dark:border-border-dark">
                            <!-- Rating Stars -->
                            <div class="flex items-center mb-4">
                                @for ($i = 1; $i <= 5; $i++)
                                    <svg class="w-5 h-5 {{ $i <= ($testimonial['rating'] ?? 5) ? 'text-yellow-400' : 'text-border-light dark:text-border-dark' }}"
                                        fill="currentColor" viewBox="0 0 20 20">
                                        <path
                                            d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                    </svg>
                                @endfor
                            </div>

                            <!-- Testimonial Content -->
                            <blockquote
                                class="text-text-secondary-light dark:text-text-secondary-dark mb-6 leading-relaxed">
                                "{{ $testimonial['content'] ?? '' }}"
                            </blockquote>

                            <!-- User Info -->
                            <div class="flex items-center">
                                <div class="flex-shrink-0 mr-4">
                                    <img class="h-12 w-12 rounded-full object-cover border-2 border-border-light dark:border-border-dark"
                                        src="{{ $testimonial['avatar'] ?? '/images/testimonials/default-avatar.jpg' }}"
                                        alt="{{ $testimonial['name'] ?? 'User' }}" loading="lazy">
                                </div>
                                <div>
                                    <h4 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark">
                                        {{ $testimonial['name'] ?? 'Anonymous' }}
                                    </h4>
                                    @if (!empty($testimonial['designation']))
                                        <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                            {{ $testimonial['designation'] }}
                                        </p>
                                    @endif
                                    @if (!empty($testimonial['location']))
                                        <p class="text-sm text-border-light dark:text-border-dark">
                                            {{ $testimonial['location'] }}
                                        </p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Call to Action -->
                <div class="text-center mt-12">
                    <p class="text-lg text-text-secondary-light dark:text-text-secondary-dark mb-6">
                        Join thousands of satisfied customers who trust our pincode service
                    </p>
                    <a href="#contact"
                        class="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light transition-colors duration-200">
                        Get Started Today
                        <svg class="ml-2 -mr-1 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                                clip-rule="evenodd" />
                        </svg>
                    </a>
                </div>
            </div>
        </section>
    @endif

    <!-- Animated Testimonials Carousel -->
    {{-- @if (isset($landingPage['testimonials']))
        <section class="py-20 bg-white dark:bg-bg-dark transition-colors duration-300">
            <div class="max-w-4xl mx-auto px-4">
                <div class="text-center mb-12" data-aos="fade-up">
                    <span
                        class="inline-block px-4 py-1 rounded-full bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-white text-xs font-semibold tracking-wider uppercase mb-3 animate-pulse">Testimonials</span>
                    <h2 class="text-3xl md:text-4xl font-extrabold mb-4 text-gray-900 dark:text-white">What Our Users
                        Say
                    </h2>
                    <p class="text-lg text-gray-600 dark:text-gray-300">Hear from businesses and individuals who rely
                        on
                        BharatPostal Info</p>
                </div>
                <div class="swiper testimonials-swiper" data-aos="fade-up" data-aos-delay="100">
                    <div class="swiper-wrapper">
                        @forelse($testimonials as $testimonial)
                            <div
                                class="swiper-slide p-8 bg-primary-50 dark:bg-bg-dark rounded-xl shadow-lg flex flex-col items-center text-center">
                                <img src="{{ $testimonial['avatar'] ?? '/images/testimonials/default-avatar.jpg' }}"
                                    class="w-16 h-16 rounded-full mb-4 shadow object-cover"
                                    alt="{{ $testimonial['name'] }}">
                                <p class="text-lg text-gray-700 dark:text-gray-300 mb-4">"{{ $testimonial['content'] }}"
                                </p>
                                <span
                                    class="font-bold text-primary-700 dark:text-primary-dark">{{ $testimonial['name'] }}</span>
                            </div>
                        @empty
                            <!-- Fallback testimonials if none are available -->
                            <div
                                class="swiper-slide p-8 bg-primary-50 dark:bg-bg-dark rounded-xl shadow-lg flex flex-col items-center text-center">
                                <img src="https://ui-avatars.com/api/?name=Amit+Sharma&background=667eea&color=fff&size=128"
                                    class="w-16 h-16 rounded-full mb-4 shadow" alt="User 1">
                                <p class="text-lg text-gray-700 dark:text-gray-300 mb-4">"The most reliable pincode
                                    directory
                                    I've used. The data is always up to date and the interface is beautiful!"</p>
                                <span class="font-bold text-primary-700 dark:text-primary-dark">Amit Sharma</span>
                                <span class="text-sm text-gray-500 dark:text-gray-400">Logistics Manager, ShipFast</span>
                            </div>
                            <div
                                class="swiper-slide p-8 bg-primary-50 dark:bg-bg-dark rounded-xl shadow-lg flex flex-col items-center text-center">
                                <img src="https://ui-avatars.com/api/?name=Priya+Patel&background=667eea&color=fff&size=128"
                                    class="w-16 h-16 rounded-full mb-4 shadow" alt="User 2">
                                <p class="text-lg text-gray-700 dark:text-gray-300 mb-4">"BharatPostal Info's API
                                    integration
                                    saved us hours of manual work. Highly recommended for any business!"</p>
                                <span class="font-bold text-primary-700 dark:text-primary-dark">Priya Patel</span>
                                <span class="text-sm text-gray-500 dark:text-gray-400">CTO, EcomXpress</span>
                            </div>
                            <div
                                class="swiper-slide p-8 bg-primary-50 dark:bg-bg-dark rounded-xl shadow-lg flex flex-col items-center text-center">
                                <img src="https://ui-avatars.com/api/?name=Rahul+Verma&background=667eea&color=fff&size=128"
                                    class="w-16 h-16 rounded-full mb-4 shadow" alt="User 3">
                                <p class="text-lg text-gray-700 dark:text-gray-300 mb-4">"Super fast search and beautiful
                                    animations. My team loves using this tool every day."</p>
                                <span class="font-bold text-primary-700 dark:text-primary-dark">Rahul Verma</span>
                                <span class="text-sm text-gray-500 dark:text-gray-400">Operations Lead, QuickMove</span>
                            </div>
                        @endforelse
                    </div>
                    <div class="swiper-pagination mt-6"></div>
                </div>
            </div>
        </section>
    @endif --}}

    <!-- Pricing/Plans Section -->
    <section class="py-20 bg-white dark:bg-bg-dark">
        <div class="max-w-6xl mx-auto px-4">
            <div class="text-center mb-16" data-aos="fade-up">
                <span
                    class="inline-block px-4 py-1 rounded-full bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark text-xs font-semibold tracking-wider uppercase mb-3">
                    Pricing
                </span>
                <h2 class="text-3xl md:text-4xl font-extrabold mb-4 text-primary-light dark:text-primary-dark">
                    Choose Your Plan</h2>
                <p class="text-lg text-text-secondary-light dark:text-text-secondary-dark">Flexible plans for
                    individuals, businesses, and enterprises</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-10">
                @forelse($plans as $index => $plan)
                    <div class="{{ $plans->count() === 3 && $index === 1 ? 'bg-white dark:bg-bg-dark rounded-xl shadow-2xl p-8 flex flex-col items-center border-4 border-primary-light dark:border-primary-dark relative' : 'bg-primary-50 dark:bg-bg-dark rounded-xl shadow-lg p-8 flex flex-col items-center border border-border-light dark:border-border-dark' }}"
                        data-aos="fade-up" data-aos-delay="{{ $index * 100 }}">
                        @if ($plans->count() === 3 && $index === 1)
                            <div
                                class="absolute -top-5 left-1/2 -translate-x-1/2 bg-primary-light dark:bg-primary-dark text-white text-xs font-bold px-4 py-1 rounded-full shadow">
                                Most Popular
                            </div>
                        @endif
                        <h3 class="font-bold text-xl mb-2 text-primary-light dark:text-primary-dark">
                            {{ $plan->name }}</h3>
                        <div class="text-4xl font-extrabold text-primary-light dark:text-primary-dark mb-4">
                            ₹{{ $plan->price == 0 ? '0' : number_format($plan->price, 2) }}
                            @if ($plan->price > 0)
                                <span
                                    class="text-base font-normal text-text-secondary-light dark:text-text-secondary-dark">/mo</span>
                            @endif
                        </div>
                        <p class="text-text-secondary-light dark:text-text-secondary-dark mb-4">
                            {{ $plan->description }}</p>
                        <ul class="mb-6 space-y-2 text-text-primary-light dark:text-text-primary-dark">
                            @foreach ($plan->features as $feature)
                                <li><i class="fa-solid fa-check text-green-500 mr-2"></i>{{ $feature }}</li>
                            @endforeach
                        </ul>
                        @if ($plan->price == 0)
                            <a href="{{ route('register', ['redirect' => route('plans.public')]) }}"
                                class="px-6 py-3 bg-primary-light dark:bg-primary-dark text-white rounded-lg font-semibold shadow hover:bg-primary-dark dark:hover:bg-primary-light transition-colors duration-300">Get
                                Started</a>
                        @elseif($plan->name === 'Enterprise' || $plan->price == null)
                            <a href="{{ route('pages.contact') }}"
                                class="px-6 py-3 bg-accent-light dark:bg-accent-dark text-primary rounded-lg font-semibold shadow hover:bg-accent-dark dark:hover:bg-accent-light transition-colors duration-300">Contact
                                Sales</a>
                        @else
                            <a href="{{ route('plans.public') }}"
                                class="px-6 py-3 bg-primary-light dark:bg-primary-dark text-white rounded-lg font-semibold shadow hover:bg-primary-dark dark:hover:bg-primary-light transition-colors duration-300">Start
                                Free Trial</a>
                        @endif
                    </div>
                @empty
                    <div class="col-span-3 text-center text-text-secondary-light dark:text-text-secondary-dark">No
                        plans available at the moment. Please check back later.</div>
                @endforelse
            </div>
        </div>
    </section>

    <!-- Latest Blog Posts -->
    @if (isset($landingPage['latest-blog-posts']))
        <section class="py-20 bg-white dark:bg-bg-dark">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center max-w-3xl mx-auto mb-16">
                    <span
                        class="inline-block px-6 py-3 rounded-full bg-primary-light/10 dark:bg-primary-dark/10 border border-primary-light dark:border-primary-dark text-primary-light dark:text-primary-dark text-sm font-semibold tracking-wider uppercase mb-6 backdrop-blur-sm">
                        Blog
                    </span>
                    <h2 class="text-4xl md:text-5xl font-extrabold mb-6 text-primary-light dark:text-primary-dark">
                        Latest from our blog
                    </h2>
                    <p class="text-xl text-text-secondary-light dark:text-text-secondary-dark leading-relaxed">
                        Learn more about postal codes, address verification, and logistics in India
                    </p>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    @forelse($latestPosts as $post)
                        <a href="{{ route('blog.show', $post->slug) }}"
                            class="block group focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark rounded-2xl transition-shadow duration-300">
                            <article
                                class="card-hover group bg-white dark:bg-bg-dark rounded-2xl shadow-lg overflow-hidden border border-border-light dark:border-border-dark">
                                <div class="relative">
                                    <img src="{{ $post->featured_image ? asset('storage/' . $post->featured_image) : '/api/placeholder/600/400' }}"
                                        alt="{{ $post->title }}"
                                        class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-500">
                                    @if ($post->category)
                                        <div
                                            class="absolute top-4 left-4 bg-primary-light dark:bg-primary-dark text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-lg">
                                            {{ $post->category->name }}
                                        </div>
                                    @endif
                                    <div
                                        class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                    </div>
                                </div>
                                <div class="p-6">
                                    <h3
                                        class="text-xl font-bold text-primary-light dark:text-primary-dark mb-3 group-hover:text-primary-dark dark:group-hover:text-primary-light transition-colors duration-300 line-clamp-2">
                                        {{ $post->title }}
                                    </h3>
                                    <p class="text-text-secondary-light dark:text-text-secondary-dark mb-4 line-clamp-3">
                                        {{ Str::limit($post->excerpt ?? strip_tags($post->content), 120) }}
                                    </p>
                                    @if ($post->tags->count() > 0)
                                        <div class="flex flex-wrap gap-2 mb-4">
                                            @foreach ($post->tags->take(2) as $tag)
                                                <span
                                                    class="inline-block px-3 py-1 bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark text-xs rounded-full border border-primary-light/20 dark:border-primary-dark/20 font-medium">
                                                    {{ $tag->name }}
                                                </span>
                                            @endforeach
                                            @if ($post->tags->count() > 2)
                                                <span
                                                    class="inline-block px-3 py-1 bg-accent-light/10 dark:bg-accent-dark/10 text-accent-light dark:text-accent-dark text-xs rounded-full border border-accent-light/20 dark:border-accent-dark/20 font-medium">
                                                    +{{ $post->tags->count() - 2 }} more
                                                </span>
                                            @endif
                                        </div>
                                    @endif
                                    <div
                                        class="flex items-center text-sm text-text-secondary-light dark:text-text-secondary-dark mb-4">
                                        <svg class="w-4 h-4 mr-2 text-primary-light dark:text-primary-dark" fill="none"
                                            stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                        </svg>
                                        <span>{{ $post->published_at ? $post->published_at->format('M j, Y') : 'Draft' }}</span>
                                        <span class="mx-2 text-accent-light dark:text-accent-dark">•</span>
                                        <span
                                            class="text-accent-light dark:text-accent-dark font-medium">{{ $post->reading_time }}
                                            min read</span>
                                    </div>
                                    <div
                                        class="flex items-center pt-3 border-t border-border-light dark:border-border-dark">
                                        <img src="{{ $post->author && $post->author->profile_photo_path ? asset('storage/' . $post->author->profile_photo_path) : '/api/placeholder/40/40' }}"
                                            alt="{{ $post->author->name ?? 'Author' }}"
                                            class="w-8 h-8 rounded-full mr-3 ring-2 ring-primary-light/20 dark:ring-primary-dark/20">
                                        <span
                                            class="text-sm font-medium text-primary-light dark:text-primary-dark">{{ $post->author->name ?? 'Anonymous' }}</span>
                                    </div>
                                </div>
                            </article>
                        </a>
                    @empty
                        <!-- Fallback blog post -->
                        <article
                            class="card-hover group block bg-white dark:bg-bg-dark rounded-2xl shadow-lg overflow-hidden border border-border-light dark:border-border-dark">
                            <div class="relative">
                                <img src="/api/placeholder/600/400" alt="Blog Post Image"
                                    class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-500">
                                <div
                                    class="absolute top-4 left-4 bg-primary-light dark:bg-primary-dark text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-lg">
                                    Example
                                </div>
                                <div
                                    class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                </div>
                            </div>
                            <div class="p-6">
                                <h3
                                    class="text-xl font-bold text-primary-light dark:text-primary-dark mb-3 group-hover:text-primary-dark dark:group-hover:text-primary-light transition-colors duration-300 line-clamp-2">
                                    Example Blog Post
                                </h3>
                                <p class="text-text-secondary-light dark:text-text-secondary-dark mb-4 line-clamp-3">
                                    This is a fallback blog post. Add posts to see dynamic content here.
                                </p>
                                <div class="flex flex-wrap gap-2 mb-4">
                                    <span
                                        class="inline-block px-3 py-1 bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark text-xs rounded-full border border-primary-light/20 dark:border-primary-dark/20 font-medium">
                                        Example Tag
                                    </span>
                                </div>
                                <div
                                    class="flex items-center text-sm text-text-secondary-light dark:text-text-secondary-dark mb-4">
                                    <svg class="w-4 h-4 mr-2 text-primary-light dark:text-primary-dark" fill="none"
                                        stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                    <span>Apr 10, 2025</span>
                                    <span class="mx-2 text-accent-light dark:text-accent-dark">•</span>
                                    <span class="text-accent-light dark:text-accent-dark font-medium">5 min read</span>
                                </div>
                                <div class="flex items-center pt-3 border-t border-border-light dark:border-border-dark">
                                    <img src="/api/placeholder/40/40" alt="Author Avatar"
                                        class="w-8 h-8 rounded-full mr-3 ring-2 ring-primary-light/20 dark:ring-primary-dark/20">
                                    <span class="text-sm font-medium text-primary-light dark:text-primary-dark">Example
                                        Author</span>
                                </div>
                            </div>
                        </article>
                    @endforelse
                </div>
                <div class="mt-12 text-center">
                    <a href="/blog"
                        class="inline-flex items-center px-8 py-4 bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark hover:bg-primary-light/20 dark:hover:bg-primary-dark/20 border border-primary-light dark:border-primary-dark text-base font-medium rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                        View all articles
                        <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                    </a>
                </div>
            </div>
        </section>
    @endif

    <!-- FAQ Accordion Section -->
    @if (isset($landingPage['faq']))
        <section class="py-20 bg-bg-light dark:bg-bg-dark">
            <div class="max-w-4xl mx-auto px-4">
                <div class="text-center mb-12" data-aos="fade-up">
                    <span
                        class="inline-block px-4 py-1 rounded-full bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark text-xs font-semibold tracking-wider uppercase mb-3 animate-pulse">FAQ</span>
                    <h2
                        class="text-3xl md:text-4xl font-extrabold mb-4 text-text-primary-light dark:text-text-primary-dark">
                        {{ $landingPage['faq']['content']['heading'] ?? 'Frequently Asked Questions' }}
                    </h2>
                </div>
                <div x-data="{ open: null }" class="space-y-4">
                    @foreach ($landingPage['faq']['content']['faqs'] ?? [] as $i => $faq)
                        <div class="bg-white dark:bg-slate-800 rounded-lg shadow-lg dark:shadow-slate-700/50 p-5 border border-border-light dark:border-border-dark"
                            data-aos="fade-up" data-aos-delay="{{ $i * 100 }}">
                            <button @click="open === {{ $i + 1 }} ? open = null : open = {{ $i + 1 }}"
                                class="flex justify-between items-center w-full text-left font-semibold text-primary-light dark:text-primary-dark text-lg focus:outline-none hover:text-accent-light dark:hover:text-accent-dark transition-colors">
                                {{ $faq['question'] ?? '' }}
                                <i :class="open === {{ $i + 1 }} ? 'fa-solid fa-chevron-up' : 'fa-solid fa-chevron-down'"
                                    class="text-primary-light dark:text-primary-dark"></i>
                            </button>
                            <div x-show="open === {{ $i + 1 }}" x-transition
                                class="mt-3 text-text-secondary-light dark:text-text-secondary-dark">
                                {{ $faq['answer'] ?? '' }}
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </section>
    @endif


    <!-- CTA Section -->
    @if (isset($landingPage['cta']))
        <section
            class="py-20 {{ $landingPage['cta']['content']['background_color'] ?? 'bg-gradient-to-r from-primary-light to-accent-light dark:from-bg-dark dark:to-primary-dark' }} text-white dark:text-text-primary-dark transition-colors duration-300">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div
                    class="relative rounded-2xl bg-white/10 dark:bg-bg-dark/80 text-white dark:text-text-primary-dark backdrop-blur-lg p-8 md:p-12 overflow-hidden shadow-2xl transition-colors duration-300 border border-white/20 dark:border-border-dark">
                    <!-- Background design elements -->
                    <div class="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
                        <div class="absolute -top-24 -left-24 w-64 h-64 rounded-full bg-white/5 dark:bg-primary-dark/20">
                        </div>
                        <div
                            class="absolute -bottom-32 -right-32 w-96 h-96 rounded-full bg-white/5 dark:bg-accent-dark/20">
                        </div>
                    </div>

                    <div class="relative z-10 md:flex md:items-center md:justify-between">
                        <div class="md:max-w-lg mb-8 md:mb-0">
                            <h2 class="text-3xl md:text-4xl font-extrabold mb-4 text-white dark:text-text-primary-dark">
                                {{ $landingPage['cta']['content']['heading'] ?? 'Ready to get started?' }}</h2>
                            <p class="text-xl text-white/80 dark:text-text-secondary-dark mb-6">
                                {{ $landingPage['cta']['content']['subheading'] ?? 'Get full access to our pincode directory with features to help you find and verify addresses across India.' }}
                            </p>
                            <ul class="space-y-3 mb-8">
                                <li class="flex items-center">
                                    <svg class="h-5 w-5 mr-2 text-accent-light dark:text-accent-dark" fill="none"
                                        stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M5 13l4 4L19 7" />
                                    </svg>
                                    <span class="text-white dark:text-text-primary-dark">Access to all 155,000+
                                        pincodes</span>
                                </li>
                                <li class="flex items-center">
                                    <svg class="h-5 w-5 mr-2 text-accent-light dark:text-accent-dark" fill="none"
                                        stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M5 13l4 4L19 7" />
                                    </svg>
                                    <span class="text-white dark:text-text-primary-dark">Bulk pincode verification</span>
                                </li>
                                <li class="flex items-center">
                                    <svg class="h-5 w-5 mr-2 text-accent-light dark:text-accent-dark" fill="none"
                                        stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M5 13l4 4L19 7" />
                                    </svg>
                                    <span class="text-white dark:text-text-primary-dark">Real-time pincode availability
                                        check</span>
                                </li>
                            </ul>
                            <div class="flex flex-col sm:flex-row gap-4">
                                <a href="{{ $landingPage['cta']['content']['cta_link'] ?? '#' }}"
                                    class="inline-flex items-center justify-center px-8 py-4 bg-accent-light dark:bg-accent-dark text-white dark:text-bg-dark hover:bg-accent-light/90 dark:hover:bg-accent-dark/90 font-medium rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                                    {{ $landingPage['cta']['content']['cta_text'] ?? 'Get Started Now' }}
                                    <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                    </svg>
                                </a>
                                <a href="#"
                                    class="inline-flex items-center justify-center px-8 py-4 bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 border border-white/30 hover:border-white/50 font-medium rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                                    Learn More
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    @endif
@endsection

@push('styles')
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.css" />
    <style>
        :root {
            --primary-50: #f0f9ff;
            --primary-100: #e0f2fe;
            --primary-200: #bae6fd;
            --primary-300: #7dd3fc;
            --primary-400: #38bdf8;
            --primary-500: #0ea5e9;
            --primary-600: #0284c7;
            --primary-700: #0369a1;
            --primary-800: #075985;
            --primary-900: #0c4a6e;
        }

        .gradient-text {
            background: linear-gradient(45deg, #4f46e5, #6366f1, #818cf8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px) scale(1.03);
            box-shadow: 0 8px 32px 0 rgba(99, 102, 241, 0.15);
        }

        /* Enhanced Animation Classes */
        .animate-float {
            animation: float 3s ease-in-out infinite;
        }

        .animate-float-complex {
            animation: float-complex 6s ease-in-out infinite;
        }

        .animate-float-gentle {
            animation: float-gentle 4s ease-in-out infinite;
        }

        .animate-bounce-slow {
            animation: bounce-slow 3s ease-in-out infinite;
        }

        .animate-bounce-gentle {
            animation: bounce-gentle 2s ease-in-out infinite;
        }

        .animate-pulse-slow {
            animation: pulse-slow 4s ease-in-out infinite;
        }

        .animate-pulse-glow {
            animation: pulse-glow 2s ease-in-out infinite;
        }

        .animate-spin-slow {
            animation: spin-slow 8s linear infinite;
        }

        .animate-wiggle {
            animation: wiggle 2s ease-in-out infinite;
        }

        .animate-slide-in-left {
            animation: slide-in-left 1s ease-out;
        }

        .animate-slide-in-right {
            animation: slide-in-right 1s ease-out;
        }

        .animate-slide-in-bottom {
            animation: slide-in-bottom 1s ease-out;
        }

        .animate-fade-in-up {
            animation: fade-in-up 1s ease-out;
        }

        .animate-pulse-border {
            animation: pulse-border 3s ease-in-out infinite;
        }

        /* Enhanced Keyframes */
        @keyframes float {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-12px); }
        }

        @keyframes float-complex {
            0%, 100% { transform: translateY(0) rotate(0deg) scale(1); }
            25% { transform: translateY(-8px) rotate(1deg) scale(1.02); }
            50% { transform: translateY(-15px) rotate(0deg) scale(1.05); }
            75% { transform: translateY(-8px) rotate(-1deg) scale(1.02); }
        }

        @keyframes float-gentle {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-8px); }
        }

        @keyframes bounce-slow {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-20px); }
        }

        @keyframes bounce-gentle {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }

        @keyframes pulse-slow {
            0%, 100% { opacity: 0.7; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.05); }
        }

        @keyframes pulse-glow {
            0%, 100% {
                box-shadow: 0 0 20px rgba(10, 88, 202, 0.3);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 0 40px rgba(10, 88, 202, 0.6);
                transform: scale(1.02);
            }
        }

        @keyframes spin-slow {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes wiggle {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(5deg); }
            75% { transform: rotate(-5deg); }
        }

        @keyframes slide-in-left {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slide-in-right {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slide-in-bottom {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fade-in-up {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse-border {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        #partners-marquee {
            position: relative;
            width: 100%;
            overflow: hidden;
        }

        .partners-track {
            will-change: transform;
        }

        .animate-blob1 {
            animation: blobMove1 18s infinite linear alternate;
        }

        .animate-blob2 {
            animation: blobMove2 22s infinite linear alternate;
        }

        .animate-blob3 {
            animation: blobMove3 20s infinite linear alternate;
        }

        @keyframes blobMove1 {
            0% {
                transform: scale(1) translate(0, 0);
            }

            50% {
                transform: scale(1.2) translate(40px, 60px);
            }

            100% {
                transform: scale(1) translate(0, 0);
            }
        }

        @keyframes blobMove2 {
            0% {
                transform: scale(1) translate(0, 0);
            }

            50% {
                transform: scale(1.1) translate(-60px, -40px);
            }

            100% {
                transform: scale(1) translate(0, 0);
            }
        }

        @keyframes blobMove3 {
            0% {
                transform: scale(1) translate(0, 0);
            }

            50% {
                transform: scale(1.15) translate(-30px, 30px);
            }

            100% {
                transform: scale(1) translate(0, 0);
            }
        }

        /* Enhanced Glass and Glow Effects with Theme Colors */
        .glass-effect {
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .enhanced-glow {
            box-shadow:
                0 0 20px rgba(10, 88, 202, 0.3),
                0 0 40px rgba(10, 88, 202, 0.2),
                0 0 60px rgba(10, 88, 202, 0.1);
            transition: all 0.3s ease;
        }

        .enhanced-glow:hover {
            box-shadow:
                0 0 30px rgba(10, 88, 202, 0.5),
                0 0 60px rgba(10, 88, 202, 0.3),
                0 0 90px rgba(10, 88, 202, 0.2);
            transform: translateY(-2px);
        }

        .dark .enhanced-glow {
            box-shadow:
                0 0 20px rgba(61, 139, 253, 0.3),
                0 0 40px rgba(61, 139, 253, 0.2),
                0 0 60px rgba(61, 139, 253, 0.1);
        }

        .dark .enhanced-glow:hover {
            box-shadow:
                0 0 30px rgba(61, 139, 253, 0.5),
                0 0 60px rgba(61, 139, 253, 0.3),
                0 0 90px rgba(61, 139, 253, 0.2);
        }

        .enhanced-gradient-text {
            background: linear-gradient(
                135deg,
                #0a58ca 0%,
                #3d8bfd 25%,
                #ffb703 50%,
                #ffda6a 75%,
                #0a58ca 100%
            );
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradient-shift 4s ease-in-out infinite;
        }

        @keyframes gradient-shift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }



        .enhanced-blob {
            filter: blur(60px);
            animation: enhanced-morph 12s ease-in-out infinite;
        }

        @keyframes enhanced-morph {
            0%, 100% {
                border-radius: 50% 50% 50% 50%;
                transform: scale(1) rotate(0deg);
            }
            20% {
                border-radius: 60% 40% 30% 70%;
                transform: scale(1.1) rotate(72deg);
            }
            40% {
                border-radius: 30% 70% 60% 40%;
                transform: scale(0.9) rotate(144deg);
            }
            60% {
                border-radius: 70% 30% 40% 60%;
                transform: scale(1.05) rotate(216deg);
            }
            80% {
                border-radius: 40% 60% 70% 30%;
                transform: scale(0.95) rotate(288deg);
            }
        }

        .floating-card {
            animation: float-gentle 4s ease-in-out infinite;
        }

        @keyframes float-gentle {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-10px);
            }
        }

        .scroll-indicator {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 6px;
            background: linear-gradient(
                90deg,
                #0a58ca 0%,
                #3d8bfd 25%,
                #ffb703 50%,
                #ffda6a 75%,
                #0a58ca 100%
            );
            background-size: 200% 100%;
            transform-origin: left;
            transform: scaleX(0);
            z-index: 1000;
            transition: transform 0.3s ease;
            animation: scroll-gradient 3s linear infinite;
            box-shadow: 0 0 10px rgba(10, 88, 202, 0.5);
        }

        @keyframes scroll-gradient {
            0% { background-position: 0% 0%; }
            100% { background-position: 200% 0%; }
        }

        .interactive-cursor {
            position: fixed;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: radial-gradient(circle, #0a58ca 0%, #3d8bfd 50%, #ffb703 100%);
            pointer-events: none;
            z-index: 9999;
            transition: all 0.2s ease;
            mix-blend-mode: difference;
            box-shadow:
                0 0 20px rgba(10, 88, 202, 0.5),
                0 0 40px rgba(61, 139, 253, 0.3);
            animation: cursor-pulse 2s ease-in-out infinite;
        }

        .dark .interactive-cursor {
            background: radial-gradient(circle, #3d8bfd 0%, #0a58ca 50%, #ffda6a 100%);
            box-shadow:
                0 0 20px rgba(61, 139, 253, 0.5),
                0 0 40px rgba(10, 88, 202, 0.3);
        }

        @keyframes cursor-pulse {
            0%, 100% { transform: scale(1); opacity: 0.8; }
            50% { transform: scale(1.2); opacity: 1; }
        }

        .parallax-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            background: linear-gradient(120deg,
                    var(--primary-light, #0a58ca) 0%,
                    var(--primary-dark, #3d8bfd) 60%,
                    var(--accent-light, #ffb703) 100%);
            transition: all 0.5s ease;
        }

        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }

        .floating-element {
            position: absolute;
            opacity: 0.1;
            animation: float-around 15s infinite linear;
        }

        @keyframes float-around {
            0% {
                transform: translateY(100vh) rotate(0deg);
            }

            100% {
                transform: translateY(-100vh) rotate(360deg);
            }
        }

        .morphing-blob {
            position: absolute;
            border-radius: 50%;
            filter: blur(40px);
            animation: morph 8s ease-in-out infinite;
            opacity: 0.3;
        }

        @keyframes morph {

            0%,
            100% {
                border-radius: 50% 50% 50% 50%;
                transform: scale(1) rotate(0deg);
            }

            25% {
                border-radius: 60% 40% 30% 70%;
                transform: scale(1.1) rotate(90deg);
            }

            50% {
                border-radius: 30% 70% 60% 40%;
                transform: scale(0.9) rotate(180deg);
            }

            75% {
                border-radius: 70% 30% 40% 60%;
                transform: scale(1.05) rotate(270deg);
            }
        }

        .interactive-bg {
            background: radial-gradient(
                circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
                rgba(10, 88, 202, 0.2) 0%,
                rgba(61, 139, 253, 0.15) 25%,
                rgba(255, 183, 3, 0.1) 50%,
                rgba(255, 218, 106, 0.05) 75%,
                transparent 100%
            );
            transition: all 0.3s ease;
        }

        .dark .interactive-bg {
            background: radial-gradient(
                circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
                rgba(61, 139, 253, 0.25) 0%,
                rgba(10, 88, 202, 0.2) 25%,
                rgba(255, 218, 106, 0.15) 50%,
                rgba(255, 183, 3, 0.1) 75%,
                transparent 100%
            );
        }

        .typing-animation {
            overflow: hidden;
            border-right: 3px solid rgba(102, 126, 234, 0.75);
            white-space: nowrap;
            animation: typing 3s steps(30, end), blink-caret 0.75s step-end infinite;
        }

        @keyframes typing {
            from {
                width: 0;
            }

            to {
                width: 100%;
            }
        }

        @keyframes blink-caret {

            from,
            to {
                border-color: transparent;
            }

            50% {
                border-color: rgba(102, 126, 234, 0.75);
            }
        }

        .card-3d,
        .perspective-card {
            perspective: 1000px;
        }

        .card-3d:hover,
        .perspective-card:hover .perspective-card-inner {
            transform: rotateY(15deg) rotateX(5deg) translateZ(50px);
        }

        .perspective-card-inner {
            transform-style: preserve-3d;
            transition: transform 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .card-front,
        .card-back {
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden;
            border-radius: 1rem;
        }

        .card-back {
            transform: rotateY(180deg);
        }

        .aurora-bg {
            background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #667eea);
            background-size: 400% 400%;
            animation: aurora 15s ease infinite;
        }

        @keyframes aurora {
            0% {
                background-position: 0% 50%;
            }

            50% {
                background-position: 100% 50%;
            }

            100% {
                background-position: 0% 50%;
            }
        }

        /* Particle System */
        .particle-system {
            pointer-events: none;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: radial-gradient(circle, #0a58ca, #3d8bfd);
            border-radius: 50%;
            opacity: 0.6;
            animation: particle-float 8s linear infinite;
        }

        .particle:nth-child(odd) {
            background: radial-gradient(circle, #ffb703, #ffda6a);
        }

        @keyframes particle-float {
            0% {
                transform: translateY(100vh) translateX(0) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 0.6;
            }
            90% {
                opacity: 0.6;
            }
            100% {
                transform: translateY(-100px) translateX(100px) rotate(360deg);
                opacity: 0;
            }
        }

        /* Enhanced card hover effects */
        .card-hover {
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .card-hover:hover {
            transform: translateY(-10px) scale(1.05) rotateX(5deg);
            box-shadow:
                0 20px 40px rgba(10, 88, 202, 0.15),
                0 10px 20px rgba(61, 139, 253, 0.1),
                0 0 0 1px rgba(255, 183, 3, 0.1);
        }

        /* Responsive enhancements */
        @media (max-width: 768px) {
            .enhanced-blob {
                filter: blur(40px);
            }

            .floating-element {
                font-size: 2rem;
            }

            .particle {
                width: 2px;
                height: 2px;
            }
        }
    </style>
@endpush

@push('scripts')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.3/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/countup.js/2.0.8/countUp.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.10.2/lottie.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // AOS Init
            AOS.init({
                once: true,
                duration: 900,
                offset: 60
            });

            // Animated Counters
            // document.querySelectorAll('.animated-counter').forEach(function(el) {
            //     const count = parseInt(el.getAttribute('data-count'));
            //     if (!isNaN(count)) {
            //         const counter = new CountUp(el, count, {
            //             duration: 2
            //         });
            //         if (!counter.error) counter.start();
            //     }
            // });

            // Lottie Animation for Hero
            if (window.lottie) {
                lottie.loadAnimation({
                    container: document.getElementById('lottie-hero'),
                    renderer: 'svg',
                    loop: true,
                    autoplay: true,
                    path: 'https://assets10.lottiefiles.com/packages/lf20_2kscui.json' // Replace with a relevant India/Map animation
                });
            }

            // Search Form
            const searchForm = document.getElementById('searchForm');
            const searchInput = document.getElementById('search');
            const typePincode = document.getElementById('type-pincode');
            const typeName = document.getElementById('type-name');
            const typeDistrict = document.getElementById('type-district');

            if (searchForm) {
                searchForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    let selectedType;
                    if (typePincode.checked) {
                        selectedType = 'pincode';
                    } else if (typeName.checked) {
                        selectedType = 'name';
                    } else if (typeDistrict.checked) {
                        selectedType = 'district';
                    } else {
                        selectedType = 'pincode'; // default
                    }
                    
                    const query = searchInput.value.trim();
                    if (!query) return;

                    let searchUrl = `/search?query=${encodeURIComponent(query)}&type=${selectedType}`;
                    
                    // Add district parameter if type is district
                    if (selectedType === 'district') {
                        searchUrl += `&district=${encodeURIComponent(query)}`;
                    }

                    window.location.href = searchUrl;
                });

                // Update placeholder text based on selected type
                [typePincode, typeName, typeDistrict].forEach(radio => {
                    radio.addEventListener('change', function() {
                        const placeholder = this.value === 'pincode' ? 'Enter pincode' :
                                         this.value === 'name' ? 'Enter post office name' :
                                         'Enter district name';
                        searchInput.placeholder = placeholder;
                    });
                });
            }

            // Swiper Testimonials
            new Swiper('.testimonials-swiper', {
                loop: true,
                autoplay: {
                    delay: 5000
                },
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true
                },
                slidesPerView: 1,
                spaceBetween: 32,
                breakpoints: {
                    768: {
                        slidesPerView: 2
                    }
                }
            });
            // GSAP Partners Marquee
            if (window.gsap) {
                gsap.to('.partners-track', {
                    xPercent: -50,
                    repeat: -1,
                    duration: 20,
                    ease: 'linear',
                });
            }

            // Enhanced GSAP Hero Text Animations
            if (window.gsap) {
                // Set initial states
                gsap.set('.hero-title span', {
                    y: 80,
                    opacity: 0,
                    scale: 0.8
                });
                gsap.set('.hero-badge', {
                    y: -50,
                    opacity: 0,
                    scale: 0.5
                });
                gsap.set('.hero-subtitle', {
                    y: 60,
                    opacity: 0
                });
                gsap.set('.hero-ctas', {
                    y: 60,
                    opacity: 0,
                    scale: 0.8
                });

                // Enhanced timeline with more dynamic animations
                const heroTimeline = gsap.timeline({ delay: 0.5 });

                heroTimeline
                    .to('.hero-badge', {
                        y: 0,
                        opacity: 1,
                        scale: 1,
                        duration: 1,
                        ease: 'back.out(1.7)',
                        onComplete: function() {
                            gsap.to('.hero-badge', {
                                boxShadow: '0 0 30px rgba(10, 88, 202, 0.4)',
                                duration: 0.5
                            });
                        }
                    })
                    .to('.hero-title span', {
                        y: 0,
                        opacity: 1,
                        scale: 1,
                        stagger: 0.2,
                        duration: 1.2,
                        ease: 'power3.out'
                    }, '-=0.5')
                    .to('.hero-subtitle', {
                        y: 0,
                        opacity: 1,
                        duration: 1,
                        ease: 'power2.out'
                    }, '-=0.8')
                    .to('.hero-ctas', {
                        y: 0,
                        opacity: 1,
                        scale: 1,
                        duration: 1,
                        ease: 'back.out(1.4)'
                    }, '-=0.6');

                // Add hover animations for CTA buttons
                gsap.utils.toArray('.hero-ctas a').forEach(button => {
                    button.addEventListener('mouseenter', () => {
                        gsap.to(button, {
                            scale: 1.05,
                            y: -5,
                            duration: 0.3,
                            ease: 'power2.out'
                        });
                    });

                    button.addEventListener('mouseleave', () => {
                        gsap.to(button, {
                            scale: 1,
                            y: 0,
                            duration: 0.3,
                            ease: 'power2.out'
                        });
                    });
                });
            }




        });
    </script>
    <script>
        // Enhanced Scroll Progress Indicator
        window.addEventListener('scroll', () => {
            const scrollProgress = window.pageYOffset / (document.documentElement.scrollHeight - window.innerHeight);
            const indicator = document.getElementById('scrollIndicator');

            if (indicator) {
                indicator.style.transform = `scaleX(${scrollProgress})`;

                // Add dynamic color based on scroll position
                const hue = scrollProgress * 360;
                indicator.style.filter = `hue-rotate(${hue}deg)`;
            }
        });

        // Enhanced Interactive Cursor
        const cursor = document.getElementById('cursor');
        let mouseX = 0, mouseY = 0;
        let cursorX = 0, cursorY = 0;
        let isHovering = false;

        document.addEventListener('mousemove', (e) => {
            mouseX = e.clientX;
            mouseY = e.clientY;
        });

        // Detect hover states for cursor enhancement
        document.addEventListener('mouseover', (e) => {
            if (e.target.matches('a, button, .card-hover, .hero-ctas a')) {
                isHovering = true;
                cursor.style.transform = 'scale(2)';
                cursor.style.opacity = '0.8';
            }
        });

        document.addEventListener('mouseout', (e) => {
            if (e.target.matches('a, button, .card-hover, .hero-ctas a')) {
                isHovering = false;
                cursor.style.transform = 'scale(1)';
                cursor.style.opacity = '1';
            }
        });

        function animateCursor() {
            const ease = isHovering ? 0.15 : 0.1;
            cursorX += (mouseX - cursorX) * ease;
            cursorY += (mouseY - cursorY) * ease;

            if (cursor) {
                cursor.style.left = cursorX + 'px';
                cursor.style.top = cursorY + 'px';
            }

            requestAnimationFrame(animateCursor);
        }
        animateCursor();

        // Enhanced Interactive Background
        const interactiveBg = document.getElementById('interactiveBg');
        let rafId;

        document.addEventListener('mousemove', (e) => {
            if (rafId) cancelAnimationFrame(rafId);

            rafId = requestAnimationFrame(() => {
                const x = (e.clientX / window.innerWidth) * 100;
                const y = (e.clientY / window.innerHeight) * 100;

                if (interactiveBg) {
                    interactiveBg.style.setProperty('--mouse-x', x + '%');
                    interactiveBg.style.setProperty('--mouse-y', y + '%');
                }
            });
        });

        // Add intersection observer for scroll-triggered animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in-up');
                }
            });
        }, observerOptions);

        // Observe elements for scroll animations
        document.querySelectorAll('.card-hover, .hero-texts > *, .search-section > *').forEach(el => {
            observer.observe(el);
        });

        // Particle System
        function createParticleSystem() {
            const particleSystem = document.getElementById('particleSystem');
            if (!particleSystem) return;

            const particleCount = window.innerWidth > 768 ? 50 : 25;

            for (let i = 0; i < particleCount; i++) {
                createParticle(particleSystem);
            }
        }

        function createParticle(container) {
            const particle = document.createElement('div');
            particle.className = 'particle';

            // Random starting position
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 8 + 's';
            particle.style.animationDuration = (8 + Math.random() * 4) + 's';

            // Random size variation
            const size = 2 + Math.random() * 4;
            particle.style.width = size + 'px';
            particle.style.height = size + 'px';

            container.appendChild(particle);

            // Remove and recreate particle after animation
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.remove();
                    createParticle(container);
                }
            }, (8 + Math.random() * 4) * 1000);
        }

        // Initialize particle system
        createParticleSystem();

        // Performance optimization: reduce animations on mobile
        if (window.innerWidth <= 768) {
            document.documentElement.style.setProperty('--animation-duration-multiplier', '0.5');
        }

        // Add smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/alpinejs/3.10.3/cdn.min.js" defer></script>
@endpush
