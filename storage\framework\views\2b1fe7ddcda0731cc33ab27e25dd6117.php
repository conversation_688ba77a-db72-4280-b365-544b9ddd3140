<?php
    $state = rawurlencode($pincodes->state);
    $district = rawurlencode($pincodes->district);
    $pincodeUrl = url("/pincodes/$state/$district/postal-code/" . rawurlencode($pincodes->pincode));
?>



<?php $__env->startSection('json-ld'); ?>
    <?php echo $__env->make('pincodes.json-ld.webpage', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <?php if(!empty($breadcrumbs)): ?>
        <?php echo $__env->make('pincodes.json-ld.breadcrumbs', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php endif; ?>

    <?php if(isset($pincodes)): ?>
        <?php
            $name = $pincodes->name;
            $streetAddress = $pincodes->name;
            $addressLocality = $pincodes->district;
            $addressRegion = $pincodes->state;
            $postalCode = $pincodes->pincode;
            $latitude = $pincodes->latitude ?? null;
            $longitude = $pincodes->longitude ?? null;
        ?>
        <?php echo $__env->make('pincodes.json-ld.places', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php endif; ?>

    <?php echo $__env->make('pincodes.json-ld.organization', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <?php if (isset($component)) { $__componentOriginal269900abaed345884ce342681cdc99f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal269900abaed345884ce342681cdc99f6 = $attributes; } ?>
<?php $component = App\View\Components\Breadcrumb::resolve(['segments' => $breadcrumbs,'pageTitle' => $pageTitle] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Breadcrumb::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['metaDescription' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($metaDescription)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal269900abaed345884ce342681cdc99f6)): ?>
<?php $attributes = $__attributesOriginal269900abaed345884ce342681cdc99f6; ?>
<?php unset($__attributesOriginal269900abaed345884ce342681cdc99f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal269900abaed345884ce342681cdc99f6)): ?>
<?php $component = $__componentOriginal269900abaed345884ce342681cdc99f6; ?>
<?php unset($__componentOriginal269900abaed345884ce342681cdc99f6); ?>
<?php endif; ?>
    <div class="container max-w-6xl mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-12 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-8">
                <?php if($pincodes): ?>
                    <div class="bg-white dark:bg-bg-dark shadow-lg rounded-2xl overflow-hidden mb-8">
                        <!-- Header -->
                        <div class="bg-gradient-to-r from-primary-light to-primary-dark dark:from-primary-dark dark:to-primary-light px-8 py-6">
                            <h2 class="text-3xl md:text-4xl font-bold text-white"><?php echo e($pageTitle); ?></h2>
                            <p class="text-white/80 mt-2">Postal Code: <?php echo e($pincodes->pincode); ?></p>
                        </div>

                        <div class="p-8">
                            <!-- Introduction -->
                            <div class="prose max-w-none text-text-secondary-light dark:text-text-secondary-dark mb-8">
                                <p class="text-lg leading-relaxed">
                                    The Post Office <span class="font-semibold"><?php echo e(ucfirst($pincodes->name)); ?></span> is
                                    a
                                    type of
                                    <span class="font-semibold"><?php echo e($pincodes->branch_type); ?></span>
                                    <?php if($pincodes->branch_type === 'BO'): ?>
                                        <span class="text-text-secondary-light dark:text-text-secondary-dark">(Branch Office)</span>
                                    <?php elseif($pincodes->branch_type === 'SO'): ?>
                                        <span class="text-text-secondary-light dark:text-text-secondary-dark">(Sub-divisional Office)</span>
                                    <?php elseif($pincodes->branch_type === 'HO'): ?>
                                        <span class="text-text-secondary-light dark:text-text-secondary-dark">(Head Office)</span>
                                    <?php endif; ?>
                                    branch, located in <span class="font-semibold"><?php echo e(ucfirst($pincodes->district)); ?></span>
                                    district of <span class="font-semibold"><?php echo e(ucfirst($pincodes->state)); ?></span>.
                                    It operates under the postal/pin code
                                    <span class="font-semibold"><?php echo e($pincodes->pincode); ?></span>.
                                    This post office is part of the
                                    <span class="font-semibold"><?php echo e($pincodes->division); ?></span>, which falls
                                    under the jurisdiction of the
                                    <span class="font-semibold"><?php echo e($pincodes->circle); ?></span>. The
                                    <?php echo e($pincodes->circle); ?> is overseen by the
                                    <span class="font-semibold"><?php echo e($pincodes->region); ?></span>.
                                </p>
                            </div>

                            <!-- Like Section -->
                            <div class="flex items-center my-8">
                                <div class="flex-grow h-px bg-border-light dark:bg-border-dark"></div>
                                <div class="flex items-center gap-3 px-4">
                                    <form id="likeForm" class="inline-block">
                                        <?php echo csrf_field(); ?>
                                        <input type="hidden" name="pincode_id" value="<?php echo e($pincodes->id); ?>">
                                        <button type="button" id="likeButton"
                                            class="flex items-center justify-center w-10 h-10 rounded-full bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors duration-200 focus:outline-none">
                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                class="h-5 w-5 text-text-secondary-light dark:text-text-secondary-dark hover:text-red-500 dark:hover:text-red-400 transition-colors duration-200"
                                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                            </svg>
                                        </button>
                                    </form>
                                    <p class="font-medium text-text-secondary-light dark:text-text-secondary-dark"><span
                                            id="likeCount"><?php echo e($pincodes->likes->count()); ?></span> likes</p>
                                </div>
                                <div class="flex-grow h-px bg-border-light dark:bg-border-dark"></div>
                            </div>

                            <!-- Info Cards -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                                <!-- Post Office Details -->
                                <div class="bg-white dark:bg-bg-dark border border-border-light dark:border-border-dark rounded-xl shadow-sm overflow-hidden">
                                    <div class="bg-blue-50 dark:bg-blue-900/20 px-6 py-4 border-b border-border-light dark:border-border-dark">
                                        <h2 class="text-xl font-bold text-primary-light dark:text-primary-dark flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none"
                                                viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                            </svg>
                                            Post Office Details
                                        </h2>
                                    </div>
                                    <div class="p-6">
                                        <table class="w-full">
                                            <tbody>
                                                <tr class="border-b border-border-light dark:border-border-dark">
                                                    <th class="py-3 text-left text-text-secondary-light dark:text-text-secondary-dark font-medium">Post Office
                                                        Name</th>
                                                    <td class="py-3 text-text-primary-light dark:text-text-primary-dark font-semibold">
                                                        <?php echo e(ucfirst($pincodes->name)); ?></td>
                                                </tr>
                                                <tr class="border-b border-border-light dark:border-border-dark">
                                                    <th class="py-3 text-left text-text-secondary-light dark:text-text-secondary-dark font-medium">Pincode Number
                                                    </th>
                                                    <td class="py-3 text-text-primary-light dark:text-text-primary-dark font-semibold">
                                                        <?php echo e($pincodes->pincode); ?></td>
                                                </tr>
                                                <?php if($pincodes->contact_number): ?>
                                                    <tr
                                                        class="border-b border-border-light dark:border-border-dark hover:bg-bg-light dark:hover:bg-gray-800 transition-colors duration-150">
                                                        <th class="py-4 pl-4 text-left text-text-secondary-light dark:text-text-secondary-dark font-medium">
                                                            <span class="flex items-center gap-2">
                                                                <svg xmlns="http://www.w3.org/2000/svg"
                                                                    class="h-4 w-4 text-text-secondary-light dark:text-text-secondary-dark" viewBox="0 0 20 20"
                                                                    fill="currentColor">
                                                                    <path
                                                                        d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                                                                </svg>
                                                                Contact Number
                                                            </span>
                                                        </th>
                                                        <td class="py-4 pr-4 text-text-primary-light dark:text-text-primary-dark font-semibold">
                                                            <div class="flex items-center justify-between">
                                                                <span
                                                                    class="text-text-primary-light dark:text-text-primary-dark"><?php echo e($pincodes->contact_number); ?></span>
                                                                <a href="#"
                                                                    onclick="event.preventDefault(); document.getElementById('contact-number-modal').classList.remove('hidden')"
                                                                    class="ml-4 px-3 py-1.5 text-sm text-primary-light dark:text-primary-dark hover:text-blue-800 dark:hover:text-blue-400 font-medium rounded-md border border-primary-light/20 dark:border-primary-dark/20 hover:border-primary-light dark:hover:border-primary-dark transition-colors duration-150 flex items-center gap-1">
                                                                    <svg xmlns="http://www.w3.org/2000/svg"
                                                                        class="h-3.5 w-3.5" viewBox="0 0 20 20"
                                                                        fill="currentColor">
                                                                        <path
                                                                            d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                                                                    </svg>
                                                                    Change Request
                                                                </a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endif; ?>
                                                <tr class="border-b border-border-light dark:border-border-dark">
                                                    <th class="py-3 text-left text-text-secondary-light dark:text-text-secondary-dark font-medium">Branch Type
                                                    </th>
                                                    <td class="py-3 text-text-primary-light dark:text-text-primary-dark font-semibold">
                                                        <?php echo e($pincodes->branch_type); ?>

                                                        <?php if($pincodes->branch_type === 'BO'): ?>
                                                            <span class="text-text-secondary-light dark:text-text-secondary-dark text-sm">(Branch Office)</span>
                                                        <?php elseif($pincodes->branch_type === 'PO'): ?>
                                                            <span class="text-text-secondary-light dark:text-text-secondary-dark text-sm">(Post
                                                                Office)</span>
                                                        <?php elseif($pincodes->branch_type === 'HO'): ?>
                                                            <span class="text-text-secondary-light dark:text-text-secondary-dark text-sm">(Head Office)</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                                <tr class="border-b border-border-light dark:border-border-dark">
                                                    <th class="py-3 text-left text-text-secondary-light dark:text-text-secondary-dark font-medium">District Name
                                                    </th>
                                                    <td class="py-3">
                                                        <a href="<?php echo e($dist_url ?? '#'); ?>"
                                                            class="text-primary-light dark:text-primary-dark hover:text-blue-800 dark:hover:text-blue-400 font-semibold hover:underline">
                                                            <?php echo e(ucfirst($pincodes->district)); ?>

                                                        </a>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th class="py-3 text-left text-text-secondary-light dark:text-text-secondary-dark font-medium">State Name</th>
                                                    <td class="py-3 text-text-primary-light dark:text-text-primary-dark font-semibold">
                                                        <?php echo e(ucfirst($pincodes->state)); ?></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <!-- Geographical Details -->
                                <div class="bg-white dark:bg-bg-dark border border-border-light dark:border-border-dark rounded-xl shadow-sm overflow-hidden">
                                    <div class="bg-blue-50 dark:bg-blue-900/20 px-6 py-4 border-b border-border-light dark:border-border-dark">
                                        <h2 class="text-xl font-bold text-primary-light dark:text-primary-dark flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none"
                                                viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                            </svg>
                                            Geographical Details
                                        </h2>
                                    </div>
                                    <div class="p-6">
                                        <table class="w-full">
                                            <tbody>
                                                <tr class="border-b border-border-light dark:border-border-dark">
                                                    <th class="py-3 text-left text-text-secondary-light dark:text-text-secondary-dark font-medium">Delivery Status
                                                    </th>
                                                    <td class="py-3">
                                                        <?php if($pincodes->delivery_status === 'Delivery'): ?>
                                                            <span
                                                                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                                                <?php echo e($pincodes->delivery_status); ?>

                                                            </span>
                                                        <?php else: ?>
                                                            <span
                                                                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">
                                                                <?php echo e($pincodes->delivery_status); ?>

                                                            </span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                                <tr class="border-b border-border-light dark:border-border-dark">
                                                    <th class="py-3 text-left text-text-secondary-light dark:text-text-secondary-dark font-medium">Division</th>
                                                    <td class="py-3 text-text-primary-light dark:text-text-primary-dark font-semibold">
                                                        <?php echo e($pincodes->division); ?></td>
                                                </tr>
                                                <tr class="border-b border-border-light dark:border-border-dark">
                                                    <th class="py-3 text-left text-text-secondary-light dark:text-text-secondary-dark font-medium">Region</th>
                                                    <td class="py-3 text-text-primary-light dark:text-text-primary-dark font-semibold">
                                                        <?php echo e($pincodes->region); ?></td>
                                                </tr>
                                                <tr class="border-b border-border-light dark:border-border-dark">
                                                    <th class="py-3 text-left text-text-secondary-light dark:text-text-secondary-dark font-medium">Circle</th>
                                                    <td class="py-3 text-text-primary-light dark:text-text-primary-dark font-semibold">
                                                        <?php echo e($pincodes->circle); ?></td>
                                                </tr>
                                                <tr>
                                                    <th class="py-3 text-left text-text-secondary-light dark:text-text-secondary-dark font-medium">Location</th>
                                                    <td class="py-3 text-text-primary-light dark:text-text-primary-dark font-semibold">
                                                        <?php echo e(ucfirst($pincodes->name)); ?></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <?php if($pincodes->branch_type === 'PO' && $pincodes->delivery_status === 'Delivery'): ?>
                                <div class="bg-bg-light dark:bg-gray-800 rounded-lg shadow-md p-6 my-5">
                                    <h2 class="text-xl font-semibold text-primary-light dark:text-primary-dark mb-4 pb-2 border-b border-border-light dark:border-border-dark">
                                        Services offered by Office</h2>
                                    <ul class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                        <li
                                            class="bg-white dark:bg-bg-dark p-3 rounded border-l-4 border-primary-light dark:border-primary-dark shadow-sm hover:shadow transition-all text-text-primary-light dark:text-text-primary-dark">
                                            Business Parcel</li>
                                        <li
                                            class="bg-white dark:bg-bg-dark p-3 rounded border-l-4 border-primary-light dark:border-primary-dark shadow-sm hover:shadow transition-all text-text-primary-light dark:text-text-primary-dark">
                                            Business Post</li>
                                        <li
                                            class="bg-white dark:bg-bg-dark p-3 rounded border-l-4 border-primary-light dark:border-primary-dark shadow-sm hover:shadow transition-all text-text-primary-light dark:text-text-primary-dark">
                                            Direct Post</li>
                                        <li
                                            class="bg-white dark:bg-bg-dark p-3 rounded border-l-4 border-primary-light dark:border-primary-dark shadow-sm hover:shadow transition-all text-text-primary-light dark:text-text-primary-dark">
                                            EMS (International Speed Post)</li>
                                        <li
                                            class="bg-white dark:bg-bg-dark p-3 rounded border-l-4 border-primary-light dark:border-primary-dark shadow-sm hover:shadow transition-all text-text-primary-light dark:text-text-primary-dark">
                                            E-Payment</li>
                                        <li
                                            class="bg-white dark:bg-bg-dark p-3 rounded border-l-4 border-primary-light dark:border-primary-dark shadow-sm hover:shadow transition-all text-text-primary-light dark:text-text-primary-dark">
                                            Epost</li>
                                        <li
                                            class="bg-white dark:bg-bg-dark p-3 rounded border-l-4 border-primary-light dark:border-primary-dark shadow-sm hover:shadow transition-all text-text-primary-light dark:text-text-primary-dark">
                                            Inland Speed Post</li>
                                        <li
                                            class="bg-white dark:bg-bg-dark p-3 rounded border-l-4 border-primary-light dark:border-primary-dark shadow-sm hover:shadow transition-all text-text-primary-light dark:text-text-primary-dark">
                                            Instant Money Order</li>
                                        <li
                                            class="bg-white dark:bg-bg-dark p-3 rounded border-l-4 border-primary-light dark:border-primary-dark shadow-sm hover:shadow transition-all text-text-primary-light dark:text-text-primary-dark">
                                            International Mails</li>
                                        <li
                                            class="bg-white dark:bg-bg-dark p-3 rounded border-l-4 border-primary-light dark:border-primary-dark shadow-sm hover:shadow transition-all text-text-primary-light dark:text-text-primary-dark">
                                            Money Order</li>
                                        <li
                                            class="bg-white dark:bg-bg-dark p-3 rounded border-l-4 border-primary-light dark:border-primary-dark shadow-sm hover:shadow transition-all text-text-primary-light dark:text-text-primary-dark">
                                            National Bill Mail Service</li>
                                        <li
                                            class="bg-white dark:bg-bg-dark p-3 rounded border-l-4 border-primary-light dark:border-primary-dark shadow-sm hover:shadow transition-all text-text-primary-light dark:text-text-primary-dark">
                                            National Pension Scheme</li>
                                        <li
                                            class="bg-white dark:bg-bg-dark p-3 rounded border-l-4 border-primary-light dark:border-primary-dark shadow-sm hover:shadow transition-all text-text-primary-light dark:text-text-primary-dark">
                                            Postal Banking Service</li>
                                        <li
                                            class="bg-white dark:bg-bg-dark p-3 rounded border-l-4 border-primary-light dark:border-primary-dark shadow-sm hover:shadow transition-all text-text-primary-light dark:text-text-primary-dark">
                                            Postal Life Insurance</li>
                                        <li
                                            class="bg-white dark:bg-bg-dark p-3 rounded border-l-4 border-primary-light dark:border-primary-dark shadow-sm hover:shadow transition-all text-text-primary-light dark:text-text-primary-dark">
                                            Postal Stationary</li>
                                        <li
                                            class="bg-white dark:bg-bg-dark p-3 rounded border-l-4 border-primary-light dark:border-primary-dark shadow-sm hover:shadow transition-all text-text-primary-light dark:text-text-primary-dark">
                                            Registered Posts</li>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <!-- Location Map -->
                            <div class="mb-8">
                                <h2 class="text-2xl font-bold text-text-primary-light dark:text-text-primary-dark mb-4 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-primary-light dark:text-primary-dark"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                                    </svg>
                                    Location Map
                                </h2>
                                <?php if(isset($pincodes->latitude) && isset($pincodes->longitude)): ?>
                                    <div class="rounded-xl overflow-hidden shadow-lg border border-border-light dark:border-border-dark">
                                        <div class="aspect-w-16 aspect-h-9">
                                            <iframe
                                                src="https://maps.google.com/maps?q=<?php echo e($pincodes->latitude); ?>,<?php echo e($pincodes->longitude); ?>&hl=es;z=14&amp;output=embed"
                                                class="w-full h-[400px]" allowfullscreen=""
                                                referrerpolicy="no-referrer-when-downgrade">
                                            </iframe>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <div class="bg-bg-light dark:bg-gray-800 rounded-xl p-8 text-center">
                                        <svg xmlns="http://www.w3.org/2000/svg"
                                            class="h-12 w-12 mx-auto text-text-secondary-light dark:text-text-secondary-dark mb-4" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                        </svg>
                                        <p class="text-text-secondary-light dark:text-text-secondary-dark text-lg">Location data is not available.</p>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Delivery Status -->
                            <div class="mb-8">
                                <h2 class="text-2xl font-bold text-text-primary-light dark:text-text-primary-dark mb-4 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-primary-light dark:text-primary-dark"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0" />
                                    </svg>
                                    Delivery Status
                                </h2>
                                <?php if($pincodes->delivery_status === 'Delivery'): ?>
                                    <div class="bg-green-50 dark:bg-green-900/20 border-l-4 border-green-500 dark:border-green-400 rounded-lg p-6">
                                        <div class="flex">
                                            <div class="flex-shrink-0">
                                                <svg class="h-5 w-5 text-green-600 dark:text-green-400" xmlns="http://www.w3.org/2000/svg"
                                                    viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd"
                                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" />
                                                </svg>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-green-800 dark:text-green-300 text-lg leading-relaxed">
                                                    As a <?php echo e($pincodes->branch_type); ?> branch,
                                                    <?php echo e(ucfirst($pincodes->name)); ?> post office plays a vital role in
                                                    providing key postal services to the local community. It facilitates
                                                    smooth communication and reliable mail delivery. Residents can visit
                                                    this post office for a variety of services, including sending and
                                                    receiving letters and parcels, along with other facilities offered
                                                    by India Post.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                <?php elseif($pincodes->delivery_status === 'Non Delivery'): ?>
                                    <div class="bg-yellow-50 dark:bg-yellow-900/20 border-l-4 border-yellow-500 dark:border-yellow-400 rounded-lg p-6">
                                        <div class="flex">
                                            <div class="flex-shrink-0">
                                                <svg class="h-5 w-5 text-yellow-600 dark:text-yellow-400" xmlns="http://www.w3.org/2000/svg"
                                                    viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd"
                                                        d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" />
                                                </svg>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-yellow-800 dark:text-yellow-300 text-lg leading-relaxed">
                                                    The delivery status for <?php echo e(ucfirst($pincodes->pincode)); ?> is marked
                                                    as 'Non Delivery', which means parcels or couriers cannot be sent to
                                                    this pincode. Any items sent mistakenly will not be delivered and
                                                    will be returned to the sender.
                                                </p>
                                                <p class="text-yellow-800 dark:text-yellow-300 text-lg mt-4">
                                                    As an alternative, consider using a different delivery method or
                                                    service provider to ensure successful delivery.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Distance Information -->
                            <?php if($distance): ?>
                                <div class="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6 mb-8 border border-border-light dark:border-border-dark">
                                    <h2 class="text-2xl font-bold text-primary-light dark:text-primary-dark mb-4 flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                                        </svg>
                                        Distance Information
                                    </h2>
                                    <p class="text-text-primary-light dark:text-text-primary-dark text-lg mb-3">
                                        The distance between <span
                                            class="font-semibold"><?php echo e(ucfirst($pincodes->name)); ?></span> and the
                                        <span class="font-semibold"><?php echo e(ucfirst($pincodes->district)); ?></span> Head
                                        Office is
                                        approximately <span class="font-bold text-primary-light dark:text-primary-dark"><?php echo e($distance); ?>

                                            kilometers</span>.
                                    </p>

                                    <p class="text-text-secondary-light dark:text-text-secondary-dark mb-3">
                                        This calculation is based on the <a
                                            href="https://pincodes.nskmultiservices.in/blog/how-to-calculate-distance-between-two-latitude-and-longitude-excel"
                                            target="_blank" class="text-primary-light dark:text-primary-dark font-medium hover:underline">Haversine
                                            formula</a>,
                                        which is also used by the E-Way Bill system to determine distances between
                                        locations.
                                    </p>
                                    <p class="text-text-secondary-light dark:text-text-secondary-dark text-sm">
                                        Please note that the distance computed using the Haversine formula usually
                                        appears shorter than the actual travel distance by road. This is because the
                                        Haversine formula determines the shortest path between two points on the Earth's
                                        surface, assuming a perfect sphere. In contrast, road distances can vary
                                        significantly due to terrain, road layouts, and traffic conditions.
                                    </p>
                                </div>
                            <?php endif; ?>

                            <!-- FAQs -->
                            <?php echo $__env->make('pincodes.partials.single_pincode_faqs', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                            <!-- Related Pincodes -->
                            <?php if (isset($component)) { $__componentOriginala13aef8a4211d3c6c40ed66f540a7512 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala13aef8a4211d3c6c40ed66f540a7512 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.related-pincodes','data' => ['relatedpincodes' => $relatedPincodes]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('related-pincodes'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['relatedpincodes' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($relatedPincodes)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala13aef8a4211d3c6c40ed66f540a7512)): ?>
<?php $attributes = $__attributesOriginala13aef8a4211d3c6c40ed66f540a7512; ?>
<?php unset($__attributesOriginala13aef8a4211d3c6c40ed66f540a7512); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala13aef8a4211d3c6c40ed66f540a7512)): ?>
<?php $component = $__componentOriginala13aef8a4211d3c6c40ed66f540a7512; ?>
<?php unset($__componentOriginala13aef8a4211d3c6c40ed66f540a7512); ?>
<?php endif; ?>

                            <!-- User Feedback -->
                            <?php echo $__env->make('pincodes.partials.reviews', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-4 px-4">
                <div class="sticky top-20">
                    <?php echo $__env->make('pincodes.partials.sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Number Change Modal -->
    <div id="contact-number-modal" class="fixed inset-0 z-50 hidden overflow-y-auto" aria-labelledby="modal-title"
        role="dialog" aria-modal="true">
        <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div
                class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <form action="<?php echo e(route('contact-number-change', $pincodes->name)); ?>" method="POST" class="p-6"
                    id="contact-number-form">
                    <?php echo csrf_field(); ?>
                    <div class="mb-4">
                        <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Request Contact Number Change</h3>
                        <p class="text-sm text-gray-500 mb-4">Please provide the new contact number for
                            <?php echo e(ucfirst($pincodes->name)); ?> post office.</p>

                        <div class="mb-4">
                            <label for="contact_number" class="block text-sm font-medium text-gray-700">New Contact
                                Number</label>
                            <input type="tel" name="contact_number" id="contact_number"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                placeholder="Enter new contact number" required>
                            <div id="contact_number_error" class="text-red-500 text-sm mt-1 hidden"></div>
                        </div>

                        <div class="mb-4">
                            <label for="reason" class="block text-sm font-medium text-gray-700">Reason for
                                Change</label>
                            <textarea name="reason" id="reason" rows="3"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                placeholder="Please provide a reason for the contact number change" required></textarea>
                            <div id="reason_error" class="text-red-500 text-sm mt-1 hidden"></div>
                        </div>
                    </div>

                    <div class="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
                        <button type="submit"
                            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:col-start-2 sm:text-sm">
                            Submit Request
                        </button>
                        <button type="button"
                            onclick="document.getElementById('contact-number-modal').classList.add('hidden')"
                            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:col-start-1 sm:text-sm">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', function() {
            const likeButton = document.getElementById('likeButton');
            const likeForm = document.getElementById('likeForm');

            // Only proceed if both elements exist
            if (likeButton && likeForm) {
                const heartIcon = likeButton.querySelector('svg');
                const likeCount = document.getElementById('likeCount');
                const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                likeButton.addEventListener('click', async function() {
                    try {
                        const pincodeId = likeForm.querySelector('input[name="pincode_id"]').value;

                        // Use FormData instead of JSON to match Laravel's expected format
                        const formData = new FormData();
                        formData.append('pincode_id', pincodeId);
                        formData.append('_token', csrfToken);

                        const response = await fetch('<?php echo e(route('likes.store')); ?>', {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': csrfToken,
                                'Accept': 'application/json'
                            },
                            body: formData
                        });

                        if (!response.ok) {
                            const errorData = await response.json().catch(() => null);
                            console.error('Server response:', errorData);
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }

                        const data = await response.json();

                        if (data.success) {
                            // Update UI
                            heartIcon.classList.add('clicked');
                            heartIcon.style.fill = 'currentColor';
                            heartIcon.classList.remove('text-gray-500');
                            heartIcon.classList.add('text-red-500');
                            likeCount.textContent = data.likes_count;

                            // Disable button after successful like
                            likeButton.disabled = true;
                        } else {
                            // Handle already liked case
                            if (data.message === 'Already liked') {
                                likeButton.disabled = true;
                                heartIcon.classList.add('text-red-500');
                                heartIcon.style.fill = 'currentColor';
                            } else {
                                console.error('Error from server:', data.message);
                                alert(data.message || 'Error adding like. Please try again.');
                            }
                        }
                    } catch (error) {
                        console.error('Error:', error);
                        alert('Error adding like. Please try again.');
                    }
                });
            }
        });
    </script>

    <script>
        // Close modal when clicking outside
        document.getElementById('contact-number-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                this.classList.add('hidden');
            }
        });

        // Close modal when pressing Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                document.getElementById('contact-number-modal').classList.add('hidden');
            }
        });

        // Handle form submission
        document.getElementById('contact-number-form').addEventListener('submit', function(e) {
            e.preventDefault();

            // Reset error messages
            document.getElementById('contact_number_error').classList.add('hidden');
            document.getElementById('reason_error').classList.add('hidden');

            const formData = new FormData(this);

            fetch(this.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute(
                            'content'),
                        'Accept': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message
                        alert(data.message);
                        // Close modal
                        document.getElementById('contact-number-modal').classList.add('hidden');
                        // Optionally refresh the page or update the contact number display
                        window.location.reload();
                    } else {
                        // Handle validation errors
                        if (data.errors) {
                            if (data.errors.contact_number) {
                                const errorDiv = document.getElementById('contact_number_error');
                                errorDiv.textContent = data.errors.contact_number[0];
                                errorDiv.classList.remove('hidden');
                            }
                            if (data.errors.reason) {
                                const errorDiv = document.getElementById('reason_error');
                                errorDiv.textContent = data.errors.reason[0];
                                errorDiv.classList.remove('hidden');
                            }
                        } else {
                            alert(data.message || 'An error occurred while submitting the request.');
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while submitting the request. Please try again.');
                });
        });
    </script>


<?php $__env->stopPush(); ?>

<?php $__env->startPush('styles'); ?>
    <style>
        .clicked {
            animation: pulse 0.5s;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.2);
            }

            100% {
                transform: scale(1);
            }
        }


    </style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/pincodes/4-pincodes-by-post-office-name.blade.php ENDPATH**/ ?>