
<div class="space-y-8">
    
    <div class="bg-white dark:bg-bg-dark rounded-lg shadow-sm border border-border-light dark:border-border-dark p-6">
        <h3 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-4">Search Posts</h3>
        <form action="<?php echo e(route('blog.index')); ?>" method="GET" class="space-y-3" id="blog-search-form">
            <div class="relative">
                <input 
                    type="text" 
                    name="search" 
                    id="blog-search-input"
                    value="<?php echo e(request('search')); ?>"
                    placeholder="Search blog posts..." 
                    class="w-full px-4 py-2 border border-border-light dark:border-border-dark rounded-lg focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-transparent bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark"
                    autocomplete="off"
                >
                <button 
                    type="submit" 
                    class="absolute right-2 top-1/2 transform -translate-y-1/2 text-text-secondary-light dark:text-text-secondary-dark hover:text-primary-light dark:hover:text-primary-dark"
                >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </button>
                <div id="blog-search-results" class="absolute z-10 w-full bg-white dark:bg-bg-dark border border-border-light dark:border-border-dark rounded-lg mt-1 shadow-lg hidden"></div>
            </div>
        </form>
    </div>

    
    <?php
        $featuredPosts = \App\Models\BlogPost::published()
            ->featured()
            ->withBasicRelations()
            ->latest('published_at')
            ->take(3)
            ->get();
    ?>
    
    <?php if($featuredPosts->count() > 0): ?>
    <div class="bg-white dark:bg-bg-dark rounded-lg shadow-sm border border-border-light dark:border-border-dark p-6">
        <h3 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-4">Featured Posts</h3>
        <div class="space-y-4">
            <?php $__currentLoopData = $featuredPosts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <article class="flex space-x-3">
                <?php if($post->featured_image): ?>
                <div class="flex-shrink-0">
                    <img 
                        src="<?php echo e(asset('storage/' . $post->featured_image)); ?>" 
                        alt="<?php echo e($post->title); ?>"
                        class="w-16 h-16 object-cover rounded-lg"
                    >
                </div>
                <?php endif; ?>
                <div class="flex-1 min-w-0">
                    <h4 class="text-sm font-medium text-text-primary-light dark:text-text-primary-dark hover:text-primary-light dark:hover:text-primary-dark transition-colors">
                        <a href="<?php echo e(route('blog.show', $post->slug)); ?>" class="line-clamp-2">
                            <?php echo e($post->title); ?>

                        </a>
                    </h4>
                    <p class="text-xs text-text-secondary-light dark:text-text-secondary-dark mt-1">
                        <?php echo e($post->published_at->format('M j, Y')); ?>

                    </p>
                    <?php if($post->category): ?>
                    <span class="inline-block px-2 py-1 text-xs text-white rounded-full mt-1 <?php echo e($post->category->badge_color); ?>">
                        <?php echo e($post->category->name); ?>

                    </span>
                    <?php endif; ?>
                </div>
            </article>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
    <?php endif; ?>

    
    <?php
        $recentPosts = \App\Models\BlogPost::published()
            ->withBasicRelations()
            ->latest('published_at')
            ->take(5)
            ->get();
    ?>
    
    <?php if($recentPosts->count() > 0): ?>
    <div class="bg-white dark:bg-bg-dark rounded-lg shadow-sm border border-border-light dark:border-border-dark p-6">
        <h3 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-4">Recent Posts</h3>
        <div class="space-y-4">
            <?php $__currentLoopData = $recentPosts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <article class="border-b border-border-light dark:border-border-dark last:border-b-0 pb-4 last:pb-0">
                <h4 class="text-sm font-medium text-text-primary-light dark:text-text-primary-dark hover:text-primary-light dark:hover:text-primary-dark transition-colors mb-1">
                    <a href="<?php echo e(route('blog.show', $post->slug)); ?>" class="line-clamp-2">
                        <?php echo e($post->title); ?>

                    </a>
                </h4>
                <div class="flex items-center justify-between text-xs text-text-secondary-light dark:text-text-secondary-dark">
                    <span><?php echo e($post->published_at->format('M j, Y')); ?></span>
                    <span><?php echo e($post->reading_time); ?> min read</span>
                </div>
                <?php if($post->excerpt): ?>
                <p class="text-xs text-text-secondary-light dark:text-text-secondary-dark mt-2 line-clamp-2"><?php echo e($post->excerpt); ?></p>
                <?php endif; ?>
            </article>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
    <?php endif; ?>

    
    <?php
        $categories = \App\Models\BlogPostCategory::withCount(['posts' => function($query) {
            $query->published();
        }])
        ->orderBy('display_order')
        ->orderBy('name')
        ->get()
        ->filter(function($category) {
            return $category->posts_count > 0;
        });
    ?>
    
    <?php if($categories->count() > 0): ?>
    <div class="bg-white dark:bg-bg-dark rounded-lg shadow-sm border border-border-light dark:border-border-dark p-6">
        <h3 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-4">Categories</h3>
        <div class="space-y-2">
            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="flex items-center justify-between">
                <a 
                    href="<?php echo e(route('blog.category', $category->slug)); ?>" 
                    class="flex items-center space-x-2 text-sm text-text-primary-light dark:text-text-primary-dark hover:text-primary-light dark:hover:text-primary-dark transition-colors"
                >
                    <span class="w-3 h-3 rounded-full <?php echo e($category->badge_color); ?>"></span>
                    <span><?php echo e($category->name); ?></span>
                </a>
                <span class="text-xs text-text-secondary-light dark:text-text-secondary-dark bg-gray-100 dark:bg-bg-darker px-2 py-1 rounded-full">
                    <?php echo e($category->posts_count); ?>

                </span>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
    <?php endif; ?>

    
    <?php
        $popularTags = \App\Models\BlogPostTag::withCount(['posts' => function($query) {
            $query->published();
        }])
        ->orderByDesc('posts_count')
        ->get()
        ->filter(function($tag) {
            return $tag->posts_count > 0;
        })
        ->take(15);
    ?>
    
    <?php if($popularTags->count() > 0): ?>
    <div class="bg-white dark:bg-bg-dark rounded-lg shadow-sm border border-border-light dark:border-border-dark p-6">
        <h3 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-4">Popular Tags</h3>
        <div class="flex flex-wrap gap-2">
            <?php $__currentLoopData = $popularTags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <a 
                href="<?php echo e(route('blog.tag', $tag->slug)); ?>" 
                class="inline-flex items-center px-3 py-1 text-xs font-medium text-text-primary-light dark:text-text-primary-dark bg-bg-light dark:bg-bg-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:text-primary-light dark:hover:text-primary-dark rounded-full transition-colors border border-border-light dark:border-border-dark"
            >
                <?php echo e($tag->name); ?>

                <span class="ml-1 text-text-primary-light dark:text-text-primary-dark">(<?php echo e($tag->posts_count); ?>)</span>
            </a>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
    <?php endif; ?>

    
    <?php
        // Database-agnostic archive query
        $dbDriver = config('database.default');
        $connection = config("database.connections.{$dbDriver}.driver");
        
        if ($connection === 'sqlite') {
            $archives = \App\Models\BlogPost::published()
                ->selectRaw('strftime("%Y", published_at) as year, strftime("%m", published_at) as month, COUNT(*) as count')
                ->groupByRaw('strftime("%Y", published_at), strftime("%m", published_at)')
                ->orderByRaw('strftime("%Y", published_at) DESC, strftime("%m", published_at) DESC')
                ->take(12)
                ->get();
        } else {
            // MySQL, PostgreSQL, etc.
            $archives = \App\Models\BlogPost::published()
                ->selectRaw('YEAR(published_at) as year, MONTH(published_at) as month, COUNT(*) as count')
                ->groupByRaw('YEAR(published_at), MONTH(published_at)')
                ->orderByRaw('YEAR(published_at) DESC, MONTH(published_at) DESC')
                ->take(12)
                ->get();
        }
    ?>
    
    <?php if($archives->count() > 0): ?>
    <div class="bg-white dark:bg-bg-dark rounded-lg shadow-sm border border-border-light dark:border-border-dark p-6">
        <h3 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-4">Archive</h3>
        <div class="space-y-2">
            <?php $__currentLoopData = $archives; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $archive): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="flex items-center justify-between">
                <a 
                    href="<?php echo e(route('blog.index', ['year' => $archive->year, 'month' => $archive->month])); ?>" 
                    class="text-sm text-text-primary-light dark:text-text-primary-dark hover:text-primary-light dark:hover:text-primary-dark transition-colors"
                >
                    <?php echo e(\Carbon\Carbon::createFromDate($archive->year, $archive->month, 1)->format('F Y')); ?>

                </a>
                <span class="text-xs text-text-secondary-light dark:text-text-secondary-dark bg-gray-100 dark:bg-bg-darker px-2 py-1 rounded-full">
                    <?php echo e($archive->count); ?>

                </span>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
    <?php endif; ?>
    
</div>


<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.autocomplete-item {
    padding: 10px;
    cursor: pointer;
    border-bottom: 1px solid var(--border-light, #f0f0f0);
}

.dark .autocomplete-item {
    border-bottom: 1px solid var(--border-dark, #2d3748);
}

.autocomplete-item:hover {
    background-color: var(--bg-lighter, #f8f9fa);
}

.dark .autocomplete-item:hover {
    background-color: var(--bg-darker, #1a202c);
}

.autocomplete-item:last-child {
    border-bottom: none;
}
</style>


<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('blog-search-input');
        const searchResults = document.getElementById('blog-search-results');
        const searchForm = document.getElementById('blog-search-form');
        let debounceTimer;

        // Function to fetch autocomplete results
        const fetchAutocomplete = (query) => {
            if (query.length < 2) {
                searchResults.innerHTML = '';
                searchResults.classList.add('hidden');
                return;
            }

            fetch(`<?php echo e(route('blog.autocomplete')); ?>?query=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    searchResults.innerHTML = '';
                    
                    if (data.length === 0) {
                        searchResults.classList.add('hidden');
                        return;
                    }

                    data.forEach(post => {
                        const item = document.createElement('div');
                        item.className = 'autocomplete-item';
                        item.textContent = post.title;
                        item.addEventListener('click', () => {
                            window.location.href = `<?php echo e(url('/blog')); ?>/${post.slug}`;
                        });
                        searchResults.appendChild(item);
                    });

                    searchResults.classList.remove('hidden');
                })
                .catch(error => {
                    console.error('Error fetching autocomplete results:', error);
                });
        };

        // Add event listener for input changes with debounce
        searchInput.addEventListener('input', function() {
            clearTimeout(debounceTimer);
            debounceTimer = setTimeout(() => {
                fetchAutocomplete(this.value);
            }, 300);
        });

        // Hide results when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
                searchResults.classList.add('hidden');
            }
        });

        // Show results when input is focused and has value
        searchInput.addEventListener('focus', function() {
            if (this.value.length >= 2) {
                fetchAutocomplete(this.value);
            }
        });

        // Prevent form submission when selecting an autocomplete item
        searchResults.addEventListener('click', function(e) {
            e.preventDefault();
        });
    });
</script><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/layouts/partials/blog-sidebar.blade.php ENDPATH**/ ?>