@php
    $allTools = App\Models\Tool::published()->get();
@endphp

<nav x-data="{ open: false, isScrolled: false, isHomePage: {{ $isHome ? 'true' : 'false' }} }" @scroll.window="isScrolled = (window.pageYOffset > 50)"
    :class="isHomePage ? (isScrolled
            ? 'bg-bg-light dark:bg-bg-dark shadow-lg text-primary-light dark:text-primary-dark'
            : 'bg-transparent shadow-none text-text-primary-light dark:text-primary-dark')
            : 'bg-bg-light dark:bg-bg-dark shadow-lg text-primary-light dark:text-primary-dark'"
    class="fixed top-0 left-0 w-full z-50 transition-all duration-300">

    <!-- Primary Navigation Menu -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
            <div class="flex items-center">
                <!-- Logo -->
                <div class="shrink-0 flex items-center">
                    <a href="{{ route('home') }}" class="flex items-center">
                        <img src="{{ uploads_url(get_setting('site_logo')) }}"
                            :class="isHomePage && !isScrolled ? 'filter invert dark:invert-0' : ''">
                    </a>
                </div>
                <!-- Navigation Links - Desktop -->
                <div class="hidden md:flex md:space-x-6 md:ms-6 lg:ms-10 lg:space-x-8">
                    <!-- Tools Dropdown -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" @click.away="open = false" :class="isHomePage && !isScrolled ?
                                'text-text-primary-light dark:text-primary-dark hover:text-text-primary-light dark:hover:text-primary-light' :
                                'text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary-light'"
                            class="inline-flex items-center px-1 pt-1 text-sm font-medium leading-5 focus:outline-none transition duration-150 ease-in-out">
                            Tools
                            <svg class="ml-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                fill="currentColor">
                                <path fill-rule="evenodd"
                                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                    clip-rule="evenodd" />
                            </svg>
                        </button>
                        <div x-show="open" x-cloak
                            class="absolute z-50 mt-2 w-48 rounded-md shadow-lg bg-bg-light dark:bg-bg-dark ring-1 ring-border-light dark:ring-border-dark">
                            <div class="py-1">
                                @foreach ($allTools as $tool)
                                    <a href="{{ route('tools.show', $tool->slug) }}"
                                        class="block px-4 py-2 text-sm text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:text-primary-light dark:hover:text-primary-dark transition-colors">
                                        {{ $tool->name }}
                                    </a>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    <!-- Pincodes Dropdown -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" @click.away="open = false" :class="isHomePage && !isScrolled ?
                                'text-text-primary-light dark:text-primary-dark hover:text-text-primary-light dark:hover:text-primary-light' :
                                'text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary-light'"
                            class="inline-flex items-center px-1 pt-1 text-sm font-medium leading-5 focus:outline-none transition duration-150 ease-in-out">
                            Pincodes
                            <svg class="ml-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                fill="currentColor">
                                <path fill-rule="evenodd"
                                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                    clip-rule="evenodd" />
                            </svg>
                        </button>
                        <div x-show="open" x-cloak
                            class="absolute z-50 mt-2 w-48 rounded-md shadow-lg bg-bg-light dark:bg-bg-dark ring-1 ring-border-light dark:ring-border-dark">
                            <div class="py-1">
                                <x-dropdown-link :href="route('pincodes.states')">All Pincodes</x-dropdown-link>
                                <x-dropdown-link :href="url('/tools/district-wise-pincode-download')">District wise
                                    Download</x-dropdown-link>
                                <x-dropdown-link :href="url('/tools/pincode-address-search-tool')">Pincode Address
                                    Search</x-dropdown-link>
                                <x-dropdown-link :href="route('courier_dict.index')">Courier
                                    Dictionary</x-dropdown-link>
                                <x-dropdown-link :href="route('api.docs.index')">API Documentation</x-dropdown-link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Search Form - Desktop -->
            <div class="hidden md:flex md:items-center md:flex-1 md:max-w-xl mx-2 lg:mx-4">
                <form action="{{ route('search') }}" method="GET" class="w-full flex">
                    <div class="relative flex-1 flex items-center">
                        <input type="text" name="query" placeholder="Search pincode or post office..."
                            class="w-full border border-border-light dark:border-border-dark bg-bg-light dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark placeholder-text-secondary-light dark:placeholder-text-secondary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark rounded-md shadow-sm text-sm transition-all duration-300" />
                        <div class="absolute right-2 flex space-x-2">
                            <label class="inline-flex items-center text-xs text-text-secondary-light dark:text-text-secondary-dark">
                                <input type="radio" name="type" value="pincode" checked
                                    class="text-primary-light dark:text-primary-dark border-border-light dark:border-border-dark focus:ring-primary-light dark:focus:ring-primary-dark" />
                                <span class="ml-1">Pincode</span>
                            </label>
                            <label class="inline-flex items-center text-xs text-text-secondary-light dark:text-text-secondary-dark">
                                <input type="radio" name="type" value="name"
                                    class="text-primary-light dark:text-primary-dark border-border-light dark:border-border-dark focus:ring-primary-light dark:focus:ring-primary-dark" />
                                <span class="ml-1">Name</span>
                            </label>
                        </div>
                    </div>
                    <button type="submit"
                        class="ml-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-text-primary-light dark:text-text-primary-dark bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:focus:ring-primary-dark transition-all duration-300">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </button>
                </form>
            </div>
            <!-- Right Side Elements -->
            <div class="flex items-center space-x-2 md:space-x-4">
                <!-- Theme Switcher Button (Desktop) -->
                <button onclick="toggleTheme()"
                    class="relative w-16 h-8 bg-border-light/30 dark:bg-border-dark/30 rounded-full p-1 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark">
                    <div
                        class="absolute left-1 top-1 w-6 h-6 bg-accent-light dark:bg-primary-dark rounded-full flex items-center justify-center transition-transform duration-300 dark:translate-x-8">
                        <span class="text-sm dark:hidden">☀️</span>
                        <span class="text-sm hidden dark:inline">🌙</span>
                    </div>
                    <div class="flex justify-between items-center px-2">
                        <span class="text-xs text-text-secondary-light dark:text-text-secondary-dark">☀️</span>
                        <span class="text-xs text-text-secondary-light dark:text-text-secondary-dark">🌙</span>
                    </div>
                </button>
                <!-- Authentication Links - Desktop -->
                <div class="hidden md:flex md:items-center">
                    @guest
                    <a href="{{ route('login') }}"
   :class="isHomePage && !isScrolled ? 'dark:accent-dark bg-accent-dark/90 dark:bg-accent-dark/70 hover:bg-accent-light/30 dark:hover:bg-accent-dark/30 backdrop-blur-sm' :
           'bg-accent-light hover:bg-accent-dark'"
   class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-text-primary-light focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent-light whitespace-nowrap transition-all duration-300">
   Login
</a>
                    @else
                        <x-dropdown align="right" width="48">
                            <x-slot name="trigger">
                                <button
                                    :class="isHomePage && !isScrolled ?
                                            'text-text-primary-light dark:text-primary-dark bg-primary-light/20 dark:bg-primary-dark/20 hover:bg-primary-light/30 dark:hover:bg-primary-dark/30' :
                                            'text-primary-light dark:text-primary-dark bg-bg-light dark:bg-bg-dark hover:text-primary-dark dark:hover:text-primary-light'"
                                    class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md focus:outline-none transition-all duration-300 backdrop-blur-sm">
                                    <div>{{ Auth::user()->name }}</div>
                                    <div class="ms-1">
                                        <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg"
                                            viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                                clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                </button>
                            </x-slot>
                            <x-slot name="content">
                                <x-dropdown-link :href="route('dashboard')">
                                    {{ __('Dashboard') }}
                                </x-dropdown-link>
                                <x-dropdown-link :href="route('profile.edit')">
                                    {{ __('Profile') }}
                                </x-dropdown-link>
                                <!-- Authentication -->
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <x-dropdown-link :href="route('logout')" onclick="event.preventDefault();
                                                        this.closest('form').submit();">
                                        {{ __('Log Out') }}
                                    </x-dropdown-link>
                                </form>
                            </x-slot>
                        </x-dropdown>
                    @endguest
                </div>
                <!-- Hamburger Menu Button -->
                <div class="md:hidden flex items-center">
                    <button @click="open = ! open"
                        :class="isHomePage && !isScrolled ?
                            'text-text-primary-light dark:text-primary-dark hover:text-text-primary-light dark:hover:text-primary-light hover:bg-primary-light/10 dark:hover:bg-primary-dark/10' :
                            'text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary-light hover:bg-primary-light/10 dark:hover:bg-primary-dark/10'"
                        class="inline-flex items-center justify-center p-2 rounded-md focus:outline-none transition-all duration-300">
                        <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                            <path :class="{ 'hidden': open, 'inline-flex': !open }" class="inline-flex"
                                stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6h16M4 12h16M4 18h16" />
                            <path :class="{ 'hidden': !open, 'inline-flex': open }" class="hidden"
                                stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <!-- Include Mobile Menu -->
    @include('layouts.partials.mobile-menu')
</nav>

<!-- Mobile Menu Scrollbar Styling -->
<style>
    /* Custom scrollbar for mobile menu */
    .mobile-menu-content {
        scrollbar-width: thin;
        scrollbar-color: rgba(10, 88, 202, 0.3) transparent;
    }

    .mobile-menu-content::-webkit-scrollbar {
        width: 6px;
    }

    .mobile-menu-content::-webkit-scrollbar-track {
        background: transparent;
    }

    .mobile-menu-content::-webkit-scrollbar-thumb {
        background: rgba(10, 88, 202, 0.3);
        border-radius: 3px;
        transition: background 0.3s ease;
    }

    .mobile-menu-content::-webkit-scrollbar-thumb:hover {
        background: rgba(10, 88, 202, 0.5);
    }

    .dark .mobile-menu-content::-webkit-scrollbar-thumb {
        background: rgba(61, 139, 253, 0.3);
    }

    .dark .mobile-menu-content::-webkit-scrollbar-thumb:hover {
        background: rgba(61, 139, 253, 0.5);
    }

    /* Ensure smooth scrolling */
    .mobile-menu-content {
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
    }

    /* Mobile menu height constraints */
    @media (max-width: 768px) {
        .mobile-menu-content {
            max-height: calc(100vh - 4rem);
            overflow-y: auto;
        }

        /* For very small screens */
        @media (max-height: 600px) {
            .mobile-menu-content {
                max-height: calc(100vh - 3rem);
            }
        }

        /* For landscape orientation on mobile */
        @media (orientation: landscape) and (max-height: 500px) {
            .mobile-menu-content {
                max-height: calc(100vh - 2rem);
            }
        }
    }
</style>

<!-- Mobile Menu Scroll Management Script -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuContent = document.querySelector('.mobile-menu-content');
    const scrollIndicatorTop = document.querySelector('.scroll-indicator-top');
    const scrollIndicatorBottom = document.querySelector('.scroll-indicator-bottom');

    if (mobileMenuContent && scrollIndicatorTop && scrollIndicatorBottom) {
        function updateScrollIndicators() {
            const { scrollTop, scrollHeight, clientHeight } = mobileMenuContent;
            const isScrollable = scrollHeight > clientHeight;
            const isAtTop = scrollTop <= 10;
            const isAtBottom = scrollTop >= scrollHeight - clientHeight - 10;

            // Show/hide indicators based on scroll position and content height
            if (isScrollable) {
                // Show bottom indicator if not at bottom
                scrollIndicatorBottom.classList.toggle('hidden', isAtBottom);
                // Show top indicator if not at top
                scrollIndicatorTop.classList.toggle('hidden', isAtTop);
            } else {
                // Hide both if content doesn't need scrolling
                scrollIndicatorTop.classList.add('hidden');
                scrollIndicatorBottom.classList.add('hidden');
            }
        }

        // Check on scroll
        mobileMenuContent.addEventListener('scroll', updateScrollIndicators);

        // Check on resize
        window.addEventListener('resize', updateScrollIndicators);

        // Check when mobile menu opens
        const mobileMenuToggle = document.querySelector('[\\@click="open = ! open"]');
        if (mobileMenuToggle) {
            mobileMenuToggle.addEventListener('click', function() {
                // Delay to allow menu to open
                setTimeout(updateScrollIndicators, 100);
            });
        }

        // Initial check
        updateScrollIndicators();

        // Check periodically in case content changes
        setInterval(updateScrollIndicators, 1000);
    }
});
</script>