<?php $__env->startSection('json-ld'); ?>
    <?php echo $__env->make('pincodes.json-ld.webpage', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <?php if(!empty($breadcrumbs)): ?>
        <?php echo $__env->make('pincodes.json-ld.breadcrumbs', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php
    $totalPincodes = count($pin_codes);
    $totalPostOffices = array_sum($po_counts);
?>

<?php $__env->startSection('content'); ?>
    <?php if (isset($component)) { $__componentOriginal269900abaed345884ce342681cdc99f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal269900abaed345884ce342681cdc99f6 = $attributes; } ?>
<?php $component = App\View\Components\Breadcrumb::resolve(['segments' => $breadcrumbs,'pageTitle' => $pageTitle] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Breadcrumb::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['metaDescription' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($metaDescription)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal269900abaed345884ce342681cdc99f6)): ?>
<?php $attributes = $__attributesOriginal269900abaed345884ce342681cdc99f6; ?>
<?php unset($__attributesOriginal269900abaed345884ce342681cdc99f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal269900abaed345884ce342681cdc99f6)): ?>
<?php $component = $__componentOriginal269900abaed345884ce342681cdc99f6; ?>
<?php unset($__componentOriginal269900abaed345884ce342681cdc99f6); ?>
<?php endif; ?>
    <div class="container mx-auto px-4 py-8 max-w-6xl">
        <div class="grid grid-cols-1 lg:grid-cols-12 gap-8">
            
            <div class="lg:col-span-8">
                <div class="bg-white dark:bg-bg-dark shadow-lg rounded-xl overflow-hidden border border-border-light dark:border-border-dark">
                    
                    <div class="bg-gradient-to-r from-primary-light to-primary-dark dark:from-primary-dark dark:to-primary-light p-8 relative overflow-hidden">
                        <div class="absolute inset-0 opacity-10">
                            <svg class="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
                                <path d="M0,0 L100,0 L100,100 L0,100 Z" fill="url(#header-pattern)" />
                            </svg>
                            <defs>
                                <pattern id="header-pattern" width="10" height="10" patternUnits="userSpaceOnUse">
                                    <circle cx="5" cy="5" r="2" fill="white" />
                                </pattern>
                            </defs>
                        </div>
                        <h2 class="text-3xl md:text-4xl font-extrabold text-white relative z-10 tracking-tight">
                            <?php echo e(ucfirst($new_district_name)); ?> District Postal Codes
                        </h2>
                        <p class="text-white/80 mt-2 max-w-2xl relative z-10">
                            Find all postal codes in <?php echo e(ucfirst($new_district_name)); ?> district of
                            <?php echo e(ucfirst($new_state_name)); ?>

                        </p>
                    </div>

                    
                    <div class="p-6 md:p-8">
                        
                        <div class="grid grid-cols-2 gap-4 mb-8">
                            <div
                                class="bg-gradient-to-br from-primary-light/10 to-primary-dark/10 dark:from-primary-dark/10 dark:to-primary-light/10 p-6 rounded-xl border border-primary-light/20 dark:border-primary-dark/20 shadow-sm">
                                <div class="flex items-center space-x-4">
                                    <div class="p-3 bg-primary-light/10 dark:bg-primary-dark/10 rounded-lg">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-light dark:text-primary-dark" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Total Pincodes</p>
                                        <h3 class="text-2xl font-bold text-primary-light dark:text-primary-dark"><?php echo e(number_format($totalPincodes)); ?>

                                        </h3>
                                    </div>
                                </div>
                            </div>
                            <div
                                class="bg-gradient-to-br from-accent-light/10 to-accent-dark/10 dark:from-accent-dark/10 dark:to-accent-light/10 p-6 rounded-xl border border-accent-light/20 dark:border-accent-dark/20 shadow-sm">
                                <div class="flex items-center space-x-4">
                                    <div class="p-3 bg-accent-light/10 dark:bg-accent-dark/10 rounded-lg">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-accent-light dark:text-accent-dark"
                                            fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark">Total Post Offices</p>
                                        <h3 class="text-2xl font-bold text-accent-light dark:text-accent-dark">
                                            <?php echo e(number_format($totalPostOffices)); ?></h3>
                                    </div>
                                </div>
                            </div>
                        </div>

                        
                        <div class="mb-8">
                            <div class="relative">
                                <input type="text" id="pincodeSearch" placeholder="Search pincodes..."
                                    class="w-full px-5 py-4 pl-12 border border-border-light dark:border-border-dark rounded-xl 
                                           focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark 
                                           focus:border-primary-light dark:focus:border-primary-dark focus:outline-none 
                                           transition duration-300 bg-bg-light dark:bg-bg-dark 
                                           text-text-primary-light dark:text-text-primary-dark">
                                <span class="absolute left-4 top-1/2 transform -translate-y-1/2 text-text-secondary-light dark:text-text-secondary-dark">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                </span>
                            </div>
                        </div>

                        
                        <div class="mb-8 bg-blue-50 dark:bg-blue-900/20 p-6 rounded-xl border border-blue-100 dark:border-blue-800/30">
                            <p class="text-text-secondary-light dark:text-text-secondary-dark">
                                Explore the comprehensive list of postal codes for
                                <span class="font-semibold"><?php echo e(ucfirst($new_district_name)); ?></span> district in
                                <?php echo e(ucfirst($new_state_name)); ?>.
                                Find pin codes for cities, towns, and villages to facilitate accurate mail delivery and
                                efficient postal services.
                            </p>
                        </div>

                        
                        <?php
                            $state = rawurlencode($new_state_name);
                            $stateUrl = url("/pincodes/$state");
                            $district = rawurlencode($new_district_name);
                            $districtUrl = "$stateUrl/$district";
                            $counter = 1;
                        ?>

                        <div id="pincodeList" class="grid grid-cols-1 gap-4">

                            <?php $__currentLoopData = $pin_codes->sortBy('pincode'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pinCode): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $pincodeUrl = "$districtUrl/postal-code/" . rawurlencode($pinCode->pincode);
                                ?>
                                <div class="pincode-item group">
                                    <div
                                        class="bg-white dark:bg-bg-dark shadow-md hover:shadow-xl rounded-xl overflow-hidden border border-border-light dark:border-border-dark hover:border-primary-light dark:hover:border-primary-dark transition-all duration-300 transform hover:-translate-y-1">
                                        <div class="p-5 flex justify-between items-center">
                                            <div class="flex items-center">
                                                <div
                                                    class="bg-primary-light dark:bg-primary-dark text-white h-8 w-8 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                                                    <?php echo e($counter); ?>

                                                </div>
                                                <div>
                                                    <h3 class="text-lg font-bold text-primary-light dark:text-primary-dark">
                                                        Pincode: <?php echo e($pinCode->pincode); ?>

                                                    </h3>
                                                    <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                                        <?php echo e($po_counts[$pinCode->pincode] ?? 0); ?> Post Office(s)
                                                    </p>
                                                </div>
                                            </div>
                                            <a href="<?php echo e($pincodeUrl); ?>"
                                                class="inline-flex items-center px-4 py-2 bg-primary-light dark:bg-primary-dark text-white rounded-lg hover:bg-accent-light dark:hover:bg-accent-dark transition-colors duration-300 text-sm font-medium">
                                                View Details
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-2" fill="none"
                                                    viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M9 5l7 7-7 7" />
                                                </svg>
                                            </a>
                                        </div>
                                        <?php if(isset($post_offices[$pinCode->pincode]) && count($post_offices[$pinCode->pincode]) > 0): ?>
                                            <div class="px-5 pb-5 pt-0 border-t border-border-light dark:border-border-dark">
                                                <p class="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark mb-2">Post Offices:</p>
                                                <div class="flex flex-wrap gap-2">
                                                    <?php $__currentLoopData = $post_offices[$pinCode->pincode]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $office): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <span
                                                            class="bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-3 py-1 rounded-full text-xs">
                                                            <a
                                                                href="<?php echo e(route('pincodes.details-by-name', [$office->state, $office->district, $office->name])); ?>">
                                                                <?php echo e(ucfirst($office->name)); ?>

                                                            </a>

                                                        </span>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php $counter++; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>

                        
                        <div id="noResults" class="hidden text-center py-8">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-text-secondary-light dark:text-text-secondary-dark mb-4"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <h3 class="text-lg font-medium text-text-primary-light dark:text-text-primary-dark">No pincodes found</h3>
                            <p class="text-text-secondary-light dark:text-text-secondary-dark mt-2">Try adjusting your search criteria</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sticky Sidebar -->
            <div class="lg:col-span-4 px-4">
                <div class="sticky top-20">
                    <?php echo $__env->make('pincodes.partials.sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>
            </div>
        </div>
    </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('pincodeSearch');
            const pincodeItems = document.querySelectorAll('.pincode-item');
            const noResultsMessage = document.getElementById('noResults');

            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase().trim();
                let resultsFound = false;

                pincodeItems.forEach(item => {
                    const pincodeText = item.textContent.toLowerCase();
                    const isVisible = pincodeText.includes(searchTerm);

                    item.style.display = isVisible ? '' : 'none';
                    if (isVisible) resultsFound = true;
                });

                // Show/hide no results message
                noResultsMessage.style.display = resultsFound ? 'none' : 'block';
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/pincodes/3-pincodes-of-single-district.blade.php ENDPATH**/ ?>