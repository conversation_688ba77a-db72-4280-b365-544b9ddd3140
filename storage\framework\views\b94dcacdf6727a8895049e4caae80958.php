<?php $__env->startSection('content'); ?>
    <?php if (isset($component)) { $__componentOriginal269900abaed345884ce342681cdc99f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal269900abaed345884ce342681cdc99f6 = $attributes; } ?>
<?php $component = App\View\Components\Breadcrumb::resolve(['segments' => $breadcrumbs,'pageTitle' => $pageTitle] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Breadcrumb::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['metaDescription' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($metaDescription)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal269900abaed345884ce342681cdc99f6)): ?>
<?php $attributes = $__attributesOriginal269900abaed345884ce342681cdc99f6; ?>
<?php unset($__attributesOriginal269900abaed345884ce342681cdc99f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal269900abaed345884ce342681cdc99f6)): ?>
<?php $component = $__componentOriginal269900abaed345884ce342681cdc99f6; ?>
<?php unset($__componentOriginal269900abaed345884ce342681cdc99f6); ?>
<?php endif; ?>
    <div class="container">
        <div class="row gy-5 ">
            <div class="col-lg-8">
                <div class="">
                   

                    <div class="container mt-5">
                        <h2 class="mb-4"><?php echo e($pageTitle); ?></h2>
                        <div class="card mb-5">
                            <div class="card-body">
                                <section class="pincode-details">
                                    <h2 class="card-title mb-4">
                                        TITLE
                                    </h2>
                                    <h3 class="card-title mb-4">User Feedback</h3>
                                    <form action="<?php echo e(route('reviews.store')); ?>" method="POST" class="mb-4">
                                        <?php echo csrf_field(); ?>
                                        <input type="hidden" name="pincode_id" value="<?php echo e($pincode_id); ?>">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">Your
                                                Name</label>
                                            <input type="text" name="name" id="name" class="form-control"
                                                required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="review" class="form-label">Review</label>
                                            <textarea name="comment" id="review" class="form-control" rows="3" required
                                                placeholder="Write your review here"></textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label for="rating" class="form-label">Rating</label>
                                            <select name="rating" id="rating" class="form-control" required>
                                                <option value="">Select a rating</option>
                                                <option value="1">1 - Poor</option>
                                                <option value="2">2 - Fair</option>
                                                <option value="3">3 - Good</option>
                                                <option value="4">4 - Very Good</option>
                                                <option value="5">5 - Excellent</option>
                                            </select>
                                        </div>
                                        <button type="submit" class="btn btn-primary">Submit
                                            Review</button>
                                    </form>

                                    <h4 class="mt-5 mb-3">Reviews</h4>
                                    <div class="review-list">
                                        <?php $__currentLoopData = $reviews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $review): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="card mb-3 bg-light">
                                                <div class="card-body">
                                                    <h5 class="card-title fw-bold">
                                                        <?php echo e($review->name); ?></h5>
                                                    <p class="card-text">
                                                        <?php echo e($review->comment); ?></p>
                                                </div>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </section>
                            </div>
                        </div>
                    </div>
                </div>


                <?php echo $__env->make('pincodes.partials.share', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                <div class="mt-4 mb-3 p-3 bg-light">
                    <p>Click following link to submit your Feedback: <a href="https://form.jotform.com/241740272022444"
                            target="_blank" class="btn btn-sm btn-primary">Feedback Form</a></p>
                </div>
            </div>

            <div class="col-lg-4">
                <?php echo $__env->make('pincodes.partials.sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script'); ?>
    <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', function() {
            const adContainers = document.querySelectorAll('.lazy-ad');
            adContainers.forEach(container => {
                loadAd(container);
            });
        });


        const loadAd = (element) => {
            const position = element.dataset.adPosition;
            const device = element.dataset.adDevice;

            fetch(`/api/ads/${position}/${device}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.text();
                })
                .then(adCode => {
                    element.innerHTML = adCode;

                    // Execute any scripts in the ad code
                    const scripts = element.getElementsByTagName('script');
                    Array.from(scripts).forEach(script => {
                        const newScript = document.createElement('script');
                        Array.from(script.attributes).forEach(attr => newScript.setAttribute(attr.name, attr
                            .value));
                        newScript.appendChild(document.createTextNode(script.innerHTML));
                        script.parentNode.replaceChild(newScript, script);
                    });

                })
                .catch(error => {
                    console.error('Error loading ad:', error);
                    element.innerHTML = 'Error loading ad';
                });
        };
    </script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/pincodes/reviews.blade.php ENDPATH**/ ?>