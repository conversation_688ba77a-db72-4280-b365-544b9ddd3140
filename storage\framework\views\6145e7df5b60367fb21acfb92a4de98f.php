<?php $__env->startSection('title', 'Latest Blog Posts'); ?>

<?php $__env->startSection('content'); ?>
    <?php if (isset($component)) { $__componentOriginal269900abaed345884ce342681cdc99f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal269900abaed345884ce342681cdc99f6 = $attributes; } ?>
<?php $component = App\View\Components\Breadcrumb::resolve(['segments' => $breadcrumbs,'pageTitle' => $pageTitle] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Breadcrumb::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['metaDescription' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($metaDescription)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal269900abaed345884ce342681cdc99f6)): ?>
<?php $attributes = $__attributesOriginal269900abaed345884ce342681cdc99f6; ?>
<?php unset($__attributesOriginal269900abaed345884ce342681cdc99f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal269900abaed345884ce342681cdc99f6)): ?>
<?php $component = $__componentOriginal269900abaed345884ce342681cdc99f6; ?>
<?php unset($__componentOriginal269900abaed345884ce342681cdc99f6); ?>
<?php endif; ?>
    <div class="container mx-auto px-4 py-6 max-w-6xl bg-bg-light dark:bg-bg-dark min-h-screen">

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 relative">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Top Ad Placement -->
                

                <?php if($posts->isEmpty()): ?>
                    <div
                        class="rounded-lg bg-white dark:bg-bg-dark shadow-lg border border-border-light dark:border-border-dark">
                        <div class="p-8 flex flex-col items-center text-center">
                            <div
                                class="w-16 h-16 mb-4 rounded-full bg-primary-light/10 dark:bg-primary-dark/10 flex items-center justify-center">
                                <svg class="w-8 h-8 text-primary-light dark:text-primary-dark" fill="none"
                                    stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z">
                                    </path>
                                </svg>
                            </div>
                            <p class="text-xl font-semibold text-text-primary-light dark:text-text-primary-dark mb-2">No
                                blog posts available</p>
                            <p class="text-text-secondary-light dark:text-text-secondary-dark">Check back soon for new
                                content</p>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="grid md:grid-cols-2 gap-6">
                        <?php $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <article
                                class="group rounded-xl bg-white dark:bg-bg-dark shadow-lg hover:shadow-xl border border-border-light dark:border-border-dark hover:scale-[1.02] transition-all duration-300 overflow-hidden">
                                <?php if($post->featured_image): ?>
                                    <figure class="relative overflow-hidden">
                                        <picture>
                                            <source srcset="<?php echo e(uploads_url($post->featured_image)); ?>">
                                            <img src="<?php echo e(uploads_url('assets/default-blog.webp')); ?>"
                                                alt="<?php echo e($post->title); ?>"
                                                class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                                                loading="lazy">
                                        </picture>
                                        <div
                                            class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                        </div>
                                    </figure>
                                <?php else: ?>
                                    <div
                                        class="h-48 bg-gradient-to-br from-primary-light/10 to-accent-light/10 dark:from-primary-dark/10 dark:to-accent-dark/10 flex items-center justify-center">
                                        <svg class="w-12 h-12 text-primary-light dark:text-primary-dark opacity-50"
                                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z">
                                            </path>
                                        </svg>
                                    </div>
                                <?php endif; ?>

                                <div class="p-6">
                                    <h2 class="text-xl font-bold mb-3 leading-tight">
                                        <a href="<?php echo e(route('blog.show', $post->slug)); ?>"
                                            class="text-text-primary-light dark:text-text-primary-dark hover:text-primary-light dark:hover:text-primary-dark transition-colors duration-200 line-clamp-2">
                                            <?php echo e($post->title); ?>

                                        </a>
                                    </h2>

                                    <div
                                        class="flex items-center text-text-secondary-light dark:text-text-secondary-dark text-sm mb-4">
                                        <div class="relative">
                                            <div
                                                class="w-8 h-8 rounded-full bg-primary-light/10 dark:bg-primary-dark/10 flex items-center justify-center">
                                                <svg class="w-4 h-4 text-primary-light dark:text-primary-dark"
                                                    fill="currentColor" viewBox="0 0 24 24">
                                                    <path
                                                        d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
                                                </svg>
                                            </div>
                                        </div>
                                        <span
                                            class="ml-2 font-medium text-text-primary-light dark:text-text-primary-dark"><?php echo e($post->author->name); ?></span>
                                        <div class="mx-2 text-text-secondary-light dark:text-text-secondary-dark">•</div>
                                        <time datetime="<?php echo e(optional($post->published_at)->toDateString()); ?>"
                                            class="text-text-secondary-light dark:text-text-secondary-dark">
                                            <?php echo e(optional($post->published_at)->diffForHumans()); ?>

                                        </time>
                                    </div>

                                    <p
                                        class="text-text-primary-light dark:text-text-primary-dark leading-relaxed mb-4 line-clamp-3">
                                        <?php echo $post->excerpt ?? Str::limit(strip_tags($post->content), 150); ?>

                                    </p>

                                    <?php if($post->tags && $post->tags->count() > 0): ?>
                                        <div class="flex flex-wrap gap-2 mt-4">
                                            <?php $__currentLoopData = $post->tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <a href="<?php echo e(route('blog.tag', $tag->slug)); ?>"
                                                    class="px-3 py-1 text-xs font-medium rounded-full border border-primary-light dark:border-primary-dark text-primary-light dark:text-primary-dark hover:bg-primary-light dark:hover:bg-primary-dark hover:text-white dark:hover:text-bg-dark transition-all duration-200 hover:scale-105">
                                                    <?php echo e($tag->name); ?>

                                                </a>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Read More Button -->
                                    <div class="mt-4 pt-4 border-t border-border-light dark:border-border-dark">
                                        <a href="<?php echo e(route('blog.show', $post->slug)); ?>"
                                            class="inline-flex items-center text-sm font-medium text-primary-light dark:text-primary-dark hover:text-accent-light dark:hover:text-accent-dark transition-colors duration-200">
                                            Read More
                                            <svg class="w-4 h-4 ml-1 transition-transform duration-200 group-hover:translate-x-1"
                                                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 5l7 7-7 7"></path>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </article>
                            <!-- between_posts Ad Placement (after every 4th post) -->
                            
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <!-- Bottom Ad Placement -->
                    

                    <!-- Pagination -->
                    <div class="mt-8 flex justify-center">
                        <div
                            class="bg-white dark:bg-bg-dark rounded-lg border border-border-light dark:border-border-dark p-4 shadow-sm">
                            <?php echo e($posts->links()); ?>

                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <div class="lg:sticky lg:top-20">
                    <div
                        class="bg-white dark:bg-bg-dark rounded-xl border border-border-light dark:border-border-dark shadow-lg p-6">
                        
                        <?php echo $__env->make('layouts.partials.blog-sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* Custom styles for line clamping */
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Custom scrollbar for dark mode */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            @apply bg-bg-light dark:bg-bg-dark;
        }

        ::-webkit-scrollbar-thumb {
            @apply bg-border-light dark:bg-border-dark rounded-full;
        }

        ::-webkit-scrollbar-thumb:hover {
            @apply bg-primary-light dark:bg-primary-dark;
        }

        /* Smooth transitions for theme switching */
        * {
            transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/blog-post/index.blade.php ENDPATH**/ ?>