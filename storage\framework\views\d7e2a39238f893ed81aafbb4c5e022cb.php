<?php
    $allTools = App\Models\Tool::published()->get();
?>

<nav x-data="{ open: false, isScrolled: false, isHomePage: <?php echo e($isHome ? 'true' : 'false'); ?> }" @scroll.window="isScrolled = (window.pageYOffset > 50)"
    :class="isHomePage ? (isScrolled
            ? 'bg-bg-light dark:bg-bg-dark shadow-lg text-primary-light dark:text-primary-dark'
            : 'bg-transparent shadow-none text-text-primary-light dark:text-primary-dark')
            : 'bg-bg-light dark:bg-bg-dark shadow-lg text-primary-light dark:text-primary-dark'"
    class="fixed top-0 left-0 w-full z-50 transition-all duration-300">

    <!-- Primary Navigation Menu -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
            <div class="flex items-center">
                <!-- Logo -->
                <div class="shrink-0 flex items-center">
                    <a href="<?php echo e(route('home')); ?>" class="flex items-center">
                        <img src="<?php echo e(uploads_url(get_setting('site_logo'))); ?>"
                            :class="isHomePage && !isScrolled ? 'filter invert dark:invert-0' : ''">
                    </a>
                </div>
                <!-- Navigation Links - Desktop -->
                <div class="hidden md:flex md:space-x-6 md:ms-6 lg:ms-10 lg:space-x-8">
                    <!-- Tools Dropdown -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" @click.away="open = false" :class="isHomePage && !isScrolled ?
                                'text-text-primary-light dark:text-primary-dark hover:text-text-primary-light dark:hover:text-primary-light' :
                                'text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary-light'"
                            class="inline-flex items-center px-1 pt-1 text-sm font-medium leading-5 focus:outline-none transition duration-150 ease-in-out">
                            Tools
                            <svg class="ml-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                fill="currentColor">
                                <path fill-rule="evenodd"
                                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                    clip-rule="evenodd" />
                            </svg>
                        </button>
                        <div x-show="open" x-cloak
                            class="absolute z-50 mt-2 w-48 rounded-md shadow-lg bg-bg-light dark:bg-bg-dark ring-1 ring-border-light dark:ring-border-dark">
                            <div class="py-1">
                                <?php $__currentLoopData = $allTools; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tool): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <a href="<?php echo e(route('tools.show', $tool->slug)); ?>"
                                        class="block px-4 py-2 text-sm text-text-secondary-light dark:text-text-secondary-dark hover:bg-primary-light/10 dark:hover:bg-primary-dark/10 hover:text-primary-light dark:hover:text-primary-dark transition-colors">
                                        <?php echo e($tool->name); ?>

                                    </a>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                    <!-- Pincodes Dropdown -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" @click.away="open = false" :class="isHomePage && !isScrolled ?
                                'text-text-primary-light dark:text-primary-dark hover:text-text-primary-light dark:hover:text-primary-light' :
                                'text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary-light'"
                            class="inline-flex items-center px-1 pt-1 text-sm font-medium leading-5 focus:outline-none transition duration-150 ease-in-out">
                            Pincodes
                            <svg class="ml-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                fill="currentColor">
                                <path fill-rule="evenodd"
                                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                    clip-rule="evenodd" />
                            </svg>
                        </button>
                        <div x-show="open" x-cloak
                            class="absolute z-50 mt-2 w-48 rounded-md shadow-lg bg-bg-light dark:bg-bg-dark ring-1 ring-border-light dark:ring-border-dark">
                            <div class="py-1">
                                <?php if (isset($component)) { $__componentOriginal68cb1971a2b92c9735f83359058f7108 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal68cb1971a2b92c9735f83359058f7108 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dropdown-link','data' => ['href' => route('pincodes.states')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dropdown-link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('pincodes.states'))]); ?>All Pincodes <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal68cb1971a2b92c9735f83359058f7108)): ?>
<?php $attributes = $__attributesOriginal68cb1971a2b92c9735f83359058f7108; ?>
<?php unset($__attributesOriginal68cb1971a2b92c9735f83359058f7108); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal68cb1971a2b92c9735f83359058f7108)): ?>
<?php $component = $__componentOriginal68cb1971a2b92c9735f83359058f7108; ?>
<?php unset($__componentOriginal68cb1971a2b92c9735f83359058f7108); ?>
<?php endif; ?>
                                <?php if (isset($component)) { $__componentOriginal68cb1971a2b92c9735f83359058f7108 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal68cb1971a2b92c9735f83359058f7108 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dropdown-link','data' => ['href' => url('/tools/district-wise-pincode-download')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dropdown-link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(url('/tools/district-wise-pincode-download'))]); ?>District wise
                                    Download <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal68cb1971a2b92c9735f83359058f7108)): ?>
<?php $attributes = $__attributesOriginal68cb1971a2b92c9735f83359058f7108; ?>
<?php unset($__attributesOriginal68cb1971a2b92c9735f83359058f7108); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal68cb1971a2b92c9735f83359058f7108)): ?>
<?php $component = $__componentOriginal68cb1971a2b92c9735f83359058f7108; ?>
<?php unset($__componentOriginal68cb1971a2b92c9735f83359058f7108); ?>
<?php endif; ?>
                                <?php if (isset($component)) { $__componentOriginal68cb1971a2b92c9735f83359058f7108 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal68cb1971a2b92c9735f83359058f7108 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dropdown-link','data' => ['href' => url('/tools/pincode-address-search-tool')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dropdown-link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(url('/tools/pincode-address-search-tool'))]); ?>Pincode Address
                                    Search <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal68cb1971a2b92c9735f83359058f7108)): ?>
<?php $attributes = $__attributesOriginal68cb1971a2b92c9735f83359058f7108; ?>
<?php unset($__attributesOriginal68cb1971a2b92c9735f83359058f7108); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal68cb1971a2b92c9735f83359058f7108)): ?>
<?php $component = $__componentOriginal68cb1971a2b92c9735f83359058f7108; ?>
<?php unset($__componentOriginal68cb1971a2b92c9735f83359058f7108); ?>
<?php endif; ?>
                                <?php if (isset($component)) { $__componentOriginal68cb1971a2b92c9735f83359058f7108 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal68cb1971a2b92c9735f83359058f7108 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dropdown-link','data' => ['href' => route('courier_dict.index')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dropdown-link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('courier_dict.index'))]); ?>Courier
                                    Dictionary <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal68cb1971a2b92c9735f83359058f7108)): ?>
<?php $attributes = $__attributesOriginal68cb1971a2b92c9735f83359058f7108; ?>
<?php unset($__attributesOriginal68cb1971a2b92c9735f83359058f7108); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal68cb1971a2b92c9735f83359058f7108)): ?>
<?php $component = $__componentOriginal68cb1971a2b92c9735f83359058f7108; ?>
<?php unset($__componentOriginal68cb1971a2b92c9735f83359058f7108); ?>
<?php endif; ?>
                                <?php if (isset($component)) { $__componentOriginal68cb1971a2b92c9735f83359058f7108 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal68cb1971a2b92c9735f83359058f7108 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dropdown-link','data' => ['href' => route('api.docs.index')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dropdown-link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('api.docs.index'))]); ?>API Documentation <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal68cb1971a2b92c9735f83359058f7108)): ?>
<?php $attributes = $__attributesOriginal68cb1971a2b92c9735f83359058f7108; ?>
<?php unset($__attributesOriginal68cb1971a2b92c9735f83359058f7108); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal68cb1971a2b92c9735f83359058f7108)): ?>
<?php $component = $__componentOriginal68cb1971a2b92c9735f83359058f7108; ?>
<?php unset($__componentOriginal68cb1971a2b92c9735f83359058f7108); ?>
<?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Search Form - Desktop -->
            <div class="hidden md:flex md:items-center md:flex-1 md:max-w-xl mx-2 lg:mx-4">
                <form action="<?php echo e(route('search')); ?>" method="GET" class="w-full flex">
                    <div class="relative flex-1 flex items-center">
                        <input type="text" name="query" placeholder="Search pincode or post office..."
                            class="w-full border border-border-light dark:border-border-dark bg-bg-light dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark placeholder-text-secondary-light dark:placeholder-text-secondary-dark focus:border-primary-light dark:focus:border-primary-dark focus:ring-primary-light dark:focus:ring-primary-dark rounded-md shadow-sm text-sm transition-all duration-300" />
                        <div class="absolute right-2 flex space-x-2">
                            <label class="inline-flex items-center text-xs text-text-secondary-light dark:text-text-secondary-dark">
                                <input type="radio" name="type" value="pincode" checked
                                    class="text-primary-light dark:text-primary-dark border-border-light dark:border-border-dark focus:ring-primary-light dark:focus:ring-primary-dark" />
                                <span class="ml-1">Pincode</span>
                            </label>
                            <label class="inline-flex items-center text-xs text-text-secondary-light dark:text-text-secondary-dark">
                                <input type="radio" name="type" value="name"
                                    class="text-primary-light dark:text-primary-dark border-border-light dark:border-border-dark focus:ring-primary-light dark:focus:ring-primary-dark" />
                                <span class="ml-1">Name</span>
                            </label>
                        </div>
                    </div>
                    <button type="submit"
                        class="ml-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-text-primary-light dark:text-text-primary-dark bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:focus:ring-primary-dark transition-all duration-300">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </button>
                </form>
            </div>
            <!-- Right Side Elements -->
            <div class="flex items-center space-x-2 md:space-x-4">
                <!-- Theme Switcher Button (Desktop) -->
                <button onclick="toggleTheme()"
                    class="relative w-16 h-8 bg-border-light/30 dark:bg-border-dark/30 rounded-full p-1 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark">
                    <div
                        class="absolute left-1 top-1 w-6 h-6 bg-accent-light dark:bg-primary-dark rounded-full flex items-center justify-center transition-transform duration-300 dark:translate-x-8">
                        <span class="text-sm dark:hidden">☀️</span>
                        <span class="text-sm hidden dark:inline">🌙</span>
                    </div>
                    <div class="flex justify-between items-center px-2">
                        <span class="text-xs text-text-secondary-light dark:text-text-secondary-dark">☀️</span>
                        <span class="text-xs text-text-secondary-light dark:text-text-secondary-dark">🌙</span>
                    </div>
                </button>
                <!-- Authentication Links - Desktop -->
                <div class="hidden md:flex md:items-center">
                    <?php if(auth()->guard()->guest()): ?>
                    <a href="<?php echo e(route('login')); ?>"
   :class="isHomePage && !isScrolled ? 'dark:accent-dark bg-accent-dark/90 dark:bg-accent-dark/70 hover:bg-accent-light/30 dark:hover:bg-accent-dark/30 backdrop-blur-sm' :
           'bg-accent-light hover:bg-accent-dark'"
   class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-text-primary-light focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent-light whitespace-nowrap transition-all duration-300">
   Login
</a>
                    <?php else: ?>
                        <?php if (isset($component)) { $__componentOriginaldf8083d4a852c446488d8d384bbc7cbe = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaldf8083d4a852c446488d8d384bbc7cbe = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dropdown','data' => ['align' => 'right','width' => '48']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dropdown'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['align' => 'right','width' => '48']); ?>
                             <?php $__env->slot('trigger', null, []); ?> 
                                <button
                                    :class="isHomePage && !isScrolled ?
                                            'text-text-primary-light dark:text-primary-dark bg-primary-light/20 dark:bg-primary-dark/20 hover:bg-primary-light/30 dark:hover:bg-primary-dark/30' :
                                            'text-primary-light dark:text-primary-dark bg-bg-light dark:bg-bg-dark hover:text-primary-dark dark:hover:text-primary-light'"
                                    class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md focus:outline-none transition-all duration-300 backdrop-blur-sm">
                                    <div><?php echo e(Auth::user()->name); ?></div>
                                    <div class="ms-1">
                                        <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg"
                                            viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                                clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                </button>
                             <?php $__env->endSlot(); ?>
                             <?php $__env->slot('content', null, []); ?> 
                                <?php if (isset($component)) { $__componentOriginal68cb1971a2b92c9735f83359058f7108 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal68cb1971a2b92c9735f83359058f7108 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dropdown-link','data' => ['href' => route('dashboard')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dropdown-link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('dashboard'))]); ?>
                                    <?php echo e(__('Dashboard')); ?>

                                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal68cb1971a2b92c9735f83359058f7108)): ?>
<?php $attributes = $__attributesOriginal68cb1971a2b92c9735f83359058f7108; ?>
<?php unset($__attributesOriginal68cb1971a2b92c9735f83359058f7108); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal68cb1971a2b92c9735f83359058f7108)): ?>
<?php $component = $__componentOriginal68cb1971a2b92c9735f83359058f7108; ?>
<?php unset($__componentOriginal68cb1971a2b92c9735f83359058f7108); ?>
<?php endif; ?>
                                <?php if (isset($component)) { $__componentOriginal68cb1971a2b92c9735f83359058f7108 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal68cb1971a2b92c9735f83359058f7108 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dropdown-link','data' => ['href' => route('profile.edit')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dropdown-link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('profile.edit'))]); ?>
                                    <?php echo e(__('Profile')); ?>

                                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal68cb1971a2b92c9735f83359058f7108)): ?>
<?php $attributes = $__attributesOriginal68cb1971a2b92c9735f83359058f7108; ?>
<?php unset($__attributesOriginal68cb1971a2b92c9735f83359058f7108); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal68cb1971a2b92c9735f83359058f7108)): ?>
<?php $component = $__componentOriginal68cb1971a2b92c9735f83359058f7108; ?>
<?php unset($__componentOriginal68cb1971a2b92c9735f83359058f7108); ?>
<?php endif; ?>
                                <!-- Authentication -->
                                <form method="POST" action="<?php echo e(route('logout')); ?>">
                                    <?php echo csrf_field(); ?>
                                    <?php if (isset($component)) { $__componentOriginal68cb1971a2b92c9735f83359058f7108 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal68cb1971a2b92c9735f83359058f7108 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dropdown-link','data' => ['href' => route('logout'),'onclick' => 'event.preventDefault();
                                                        this.closest(\'form\').submit();']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dropdown-link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('logout')),'onclick' => 'event.preventDefault();
                                                        this.closest(\'form\').submit();']); ?>
                                        <?php echo e(__('Log Out')); ?>

                                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal68cb1971a2b92c9735f83359058f7108)): ?>
<?php $attributes = $__attributesOriginal68cb1971a2b92c9735f83359058f7108; ?>
<?php unset($__attributesOriginal68cb1971a2b92c9735f83359058f7108); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal68cb1971a2b92c9735f83359058f7108)): ?>
<?php $component = $__componentOriginal68cb1971a2b92c9735f83359058f7108; ?>
<?php unset($__componentOriginal68cb1971a2b92c9735f83359058f7108); ?>
<?php endif; ?>
                                </form>
                             <?php $__env->endSlot(); ?>
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaldf8083d4a852c446488d8d384bbc7cbe)): ?>
<?php $attributes = $__attributesOriginaldf8083d4a852c446488d8d384bbc7cbe; ?>
<?php unset($__attributesOriginaldf8083d4a852c446488d8d384bbc7cbe); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaldf8083d4a852c446488d8d384bbc7cbe)): ?>
<?php $component = $__componentOriginaldf8083d4a852c446488d8d384bbc7cbe; ?>
<?php unset($__componentOriginaldf8083d4a852c446488d8d384bbc7cbe); ?>
<?php endif; ?>
                    <?php endif; ?>
                </div>
                <!-- Hamburger Menu Button -->
                <div class="md:hidden flex items-center">
                    <button @click="open = ! open"
                        :class="isHomePage && !isScrolled ?
                            'text-text-primary-light dark:text-primary-dark hover:text-text-primary-light dark:hover:text-primary-light hover:bg-primary-light/10 dark:hover:bg-primary-dark/10' :
                            'text-primary-light dark:text-primary-dark hover:text-primary-dark dark:hover:text-primary-light hover:bg-primary-light/10 dark:hover:bg-primary-dark/10'"
                        class="inline-flex items-center justify-center p-2 rounded-md focus:outline-none transition-all duration-300">
                        <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                            <path :class="{ 'hidden': open, 'inline-flex': !open }" class="inline-flex"
                                stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6h16M4 12h16M4 18h16" />
                            <path :class="{ 'hidden': !open, 'inline-flex': open }" class="hidden"
                                stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <!-- Include Mobile Menu -->
    <?php echo $__env->make('layouts.partials.mobile-menu', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
</nav>

<!-- Mobile Menu Scrollbar Styling -->
<style>
    /* Custom scrollbar for mobile menu */
    .mobile-menu-content {
        scrollbar-width: thin;
        scrollbar-color: rgba(10, 88, 202, 0.3) transparent;
    }

    .mobile-menu-content::-webkit-scrollbar {
        width: 6px;
    }

    .mobile-menu-content::-webkit-scrollbar-track {
        background: transparent;
    }

    .mobile-menu-content::-webkit-scrollbar-thumb {
        background: rgba(10, 88, 202, 0.3);
        border-radius: 3px;
        transition: background 0.3s ease;
    }

    .mobile-menu-content::-webkit-scrollbar-thumb:hover {
        background: rgba(10, 88, 202, 0.5);
    }

    .dark .mobile-menu-content::-webkit-scrollbar-thumb {
        background: rgba(61, 139, 253, 0.3);
    }

    .dark .mobile-menu-content::-webkit-scrollbar-thumb:hover {
        background: rgba(61, 139, 253, 0.5);
    }

    /* Ensure smooth scrolling */
    .mobile-menu-content {
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
    }

    /* Mobile menu height constraints */
    @media (max-width: 768px) {
        .mobile-menu-content {
            max-height: calc(100vh - 4rem);
            overflow-y: auto;
        }

        /* For very small screens */
        @media (max-height: 600px) {
            .mobile-menu-content {
                max-height: calc(100vh - 3rem);
            }
        }

        /* For landscape orientation on mobile */
        @media (orientation: landscape) and (max-height: 500px) {
            .mobile-menu-content {
                max-height: calc(100vh - 2rem);
            }
        }
    }
</style>

<!-- Mobile Menu Scroll Management Script -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuContent = document.querySelector('.mobile-menu-content');
    const scrollIndicatorTop = document.querySelector('.scroll-indicator-top');
    const scrollIndicatorBottom = document.querySelector('.scroll-indicator-bottom');

    if (mobileMenuContent && scrollIndicatorTop && scrollIndicatorBottom) {
        function updateScrollIndicators() {
            const { scrollTop, scrollHeight, clientHeight } = mobileMenuContent;
            const isScrollable = scrollHeight > clientHeight;
            const isAtTop = scrollTop <= 10;
            const isAtBottom = scrollTop >= scrollHeight - clientHeight - 10;

            // Show/hide indicators based on scroll position and content height
            if (isScrollable) {
                // Show bottom indicator if not at bottom
                scrollIndicatorBottom.classList.toggle('hidden', isAtBottom);
                // Show top indicator if not at top
                scrollIndicatorTop.classList.toggle('hidden', isAtTop);
            } else {
                // Hide both if content doesn't need scrolling
                scrollIndicatorTop.classList.add('hidden');
                scrollIndicatorBottom.classList.add('hidden');
            }
        }

        // Check on scroll
        mobileMenuContent.addEventListener('scroll', updateScrollIndicators);

        // Check on resize
        window.addEventListener('resize', updateScrollIndicators);

        // Check when mobile menu opens
        const mobileMenuToggle = document.querySelector('[\\@click="open = ! open"]');
        if (mobileMenuToggle) {
            mobileMenuToggle.addEventListener('click', function() {
                // Delay to allow menu to open
                setTimeout(updateScrollIndicators, 100);
            });
        }

        // Initial check
        updateScrollIndicators();

        // Check periodically in case content changes
        setInterval(updateScrollIndicators, 1000);
    }
});
</script><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/layouts/navigation.blade.php ENDPATH**/ ?>