<div class="container mx-auto py-8 px-4">
    <?php
        $index = 1;
    ?>

    <?php if($relatedpincodes->isNotEmpty()): ?>
        <h2
            class="text-2xl font-bold text-text-primary-light dark:text-text-primary-dark mb-6 border-b border-border-light dark:border-border-dark pb-2">
            Other Post Offices with the Same Pincode</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <?php $__currentLoopData = $relatedpincodes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pincode): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php
                    $state = rawurlencode($pincode->state);
                    $district = rawurlencode($pincode->district);
                    $name = rawurlencode($pincode->name);
                    $url = url("/pincodes/$state/$district/$name");
                ?>

                <div
                    class="bg-white dark:bg-bg-dark rounded-lg shadow-md overflow-hidden border border-border-light dark:border-border-dark hover:shadow-lg transition-shadow duration-300">
                    <div class="p-5">
                        <div class="flex items-center mb-4">
                            <span
                                class="flex items-center justify-center w-8 h-8 rounded-full bg-primary-light dark:bg-primary-dark text-white font-bold text-sm mr-3"><?php echo e($index); ?></span>
                            <h3 class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark">Pincode:
                                <?php echo e($pincode->pincode); ?></h3>
                        </div>
                        <div class="space-y-2 text-text-secondary-light dark:text-text-secondary-dark">
                            <div class="flex">
                                <span class="font-medium w-20">Name:</span>
                                <a href="<?php echo e($url); ?>"
                                    class="text-primary-light dark:text-primary-dark hover:text-blue-800 dark:hover:text-blue-400 hover:underline">
                                    <?php echo e(ucfirst($pincode->name)); ?>

                                </a>
                            </div>
                            <div class="flex">
                                <span class="font-medium w-20">District:</span>
                                <span><?php echo e(ucfirst($pincode->district)); ?></span>
                            </div>
                            <div class="flex">
                                <span class="font-medium w-20">Division:</span>
                                <span><?php echo e($pincode->division); ?></span>
                            </div>
                            <div class="flex">
                                <span class="font-medium w-20">Circle:</span>
                                <span><?php echo e($pincode->circle); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
                <?php
                    $index++;
                ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    <?php endif; ?>
</div><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/components/related-pincodes.blade.php ENDPATH**/ ?>