<?php $__env->startSection('json-ld'); ?>
    <?php echo $__env->make('pincodes.json-ld.webpage', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <?php if(!empty($breadcrumbs)): ?>
        <?php echo $__env->make('pincodes.json-ld.breadcrumbs', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <?php if (isset($component)) { $__componentOriginal269900abaed345884ce342681cdc99f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal269900abaed345884ce342681cdc99f6 = $attributes; } ?>
<?php $component = App\View\Components\Breadcrumb::resolve(['segments' => $breadcrumbs,'pageTitle' => $pageTitle] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Breadcrumb::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['metaDescription' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($metaDescription)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal269900abaed345884ce342681cdc99f6)): ?>
<?php $attributes = $__attributesOriginal269900abaed345884ce342681cdc99f6; ?>
<?php unset($__attributesOriginal269900abaed345884ce342681cdc99f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal269900abaed345884ce342681cdc99f6)): ?>
<?php $component = $__componentOriginal269900abaed345884ce342681cdc99f6; ?>
<?php unset($__componentOriginal269900abaed345884ce342681cdc99f6); ?>
<?php endif; ?>

    <div class="container max-w-6xl mx-auto px-4 py-8">
        <!-- Stats Overview -->
        <div class="mb-12">
            <h2 class="text-2xl font-bold text-text-primary-light dark:text-text-primary-dark mb-6">Overview</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Total States Card -->
                <div
                    class="bg-bg-light dark:bg-bg-dark border border-border-light dark:border-border-dark rounded-lg shadow-lg p-6">
                    <div class="flex items-center">
                        <div
                            class="p-3 rounded-full bg-primary-light/10 dark:bg-primary-dark/10 text-primary-light dark:text-primary-dark">
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z" />
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">Total States and UTs
                            </p>
                            <h3 class="text-xl font-bold text-text-primary-light dark:text-text-primary-dark">
                                <?php echo e($statesData->count()); ?>

                            </h3>
                        </div>
                    </div>
                </div>

                <!-- Total Pincodes Card -->
                <div
                    class="bg-bg-light dark:bg-bg-dark border border-border-light dark:border-border-dark rounded-lg shadow-lg p-6">
                    <div class="flex items-center">
                        <div
                            class="p-3 rounded-full bg-green-500/10 text-green-500 dark:bg-green-400/10 dark:text-green-400">
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">Total Post Offices
                            </p>
                            <h3 class="text-xl font-bold text-text-primary-light dark:text-text-primary-dark">
                                <?php echo e(number_format($totalPincodes)); ?>

                            </h3>
                        </div>
                    </div>
                </div>

                <!-- Average Pincodes Card -->
                <div
                    class="bg-bg-light dark:bg-bg-dark border border-border-light dark:border-border-dark rounded-lg shadow-lg p-6">
                    <div class="flex items-center">
                        <div
                            class="p-3 rounded-full bg-purple-500/10 text-purple-500 dark:bg-purple-400/10 dark:text-purple-400">
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">Avg. Post
                                Offices/State</p>
                            <h3 class="text-xl font-bold text-text-primary-light dark:text-text-primary-dark">
                                <?php echo e($statesData->count() > 0 ? number_format($totalPincodes / $statesData->count(), 0) : 0); ?>

                            </h3>
                        </div>
                    </div>
                </div>

                <!-- Largest State Card -->
                <div
                    class="bg-bg-light dark:bg-bg-dark border border-border-light dark:border-border-dark rounded-lg shadow-lg p-6">
                    <div class="flex items-center">
                        <div
                            class="p-3 rounded-full bg-accent-light/10 dark:bg-accent-dark/10 text-accent-light dark:text-accent-dark">
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-text-secondary-light dark:text-text-secondary-dark">Largest State</p>
                            <h3 class="text-xl font-bold text-text-primary-light dark:text-text-primary-dark">
                                <?php echo e(ucfirst($statesData->sortByDesc('count')->keys()->first())); ?>

                            </h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div class="lg:col-span-3">
                <div
                    class="bg-bg-light dark:bg-bg-dark border border-border-light dark:border-border-dark rounded-xl shadow-lg">
                    <!-- Header with Search -->
                    <div class="border-b border-border-light dark:border-border-dark p-6">
                        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                            <h2 class="text-2xl font-bold text-text-primary-light dark:text-text-primary-dark">
                                <?php echo e($pageTitle); ?>

                            </h2>
                            <div class="relative">
                                <input type="text" id="stateSearch" class="w-full md:w-72 px-4 py-2.5 border border-border-light dark:border-border-dark rounded-lg 
                                                          focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-primary-light dark:focus:border-primary-dark
                                                          bg-bg-light dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark pl-10
                                                          transition-colors duration-200" placeholder="Search states...">
                                <svg class="w-5 h-5 text-text-secondary-light dark:text-text-secondary-dark absolute left-3 top-3"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                            </div>
                        </div>
                    </div>

                    <!-- States Grid -->
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="statesGrid">
                            <?php $__currentLoopData = $statesData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stateName => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div
                                    class="state-card bg-bg-light dark:bg-bg-dark border border-border-light dark:border-border-dark rounded-lg overflow-hidden
                                                                    hover:shadow-xl hover:-translate-y-1 hover:border-primary-light dark:hover:border-primary-dark transition-all duration-300">
                                    <a href="<?php echo e(url('/pincodes/' . rawurlencode($stateName))); ?>" class="block p-5">
                                        <h3
                                            class="text-lg font-semibold text-text-primary-light dark:text-text-primary-dark mb-4">
                                            <?php echo e(ucfirst($stateName)); ?>

                                        </h3>
                                        <div class="space-y-3">
                                            <div class="flex justify-between items-center text-sm">
                                                <span class="text-text-secondary-light dark:text-text-secondary-dark">Total
                                                    Pincodes:</span>
                                                <span class="font-medium text-text-primary-light dark:text-text-primary-dark">
                                                    <?php echo e(number_format($data['count'])); ?>

                                                </span>
                                            </div>
                                            <div class="flex justify-between items-center text-sm">
                                                <span
                                                    class="text-text-secondary-light dark:text-text-secondary-dark">Districts:</span>
                                                <span class="font-medium text-text-primary-light dark:text-text-primary-dark">
                                                    <?php echo e(number_format($data['districts_count'])); ?>

                                                </span>
                                            </div>
                                            <div class="flex justify-between items-center text-sm">
                                                <span class="text-text-secondary-light dark:text-text-secondary-dark">% of
                                                    Total:</span>
                                                <span class="font-medium text-text-primary-light dark:text-text-primary-dark">
                                                    <?php echo e(number_format($data['percentage'], 1)); ?>%
                                                </span>
                                            </div>
                                            <div class="pt-4 flex justify-end">
                                                <span
                                                    class="inline-flex items-center text-primary-light dark:text-primary-dark 
                                                                                   hover:text-accent-light dark:hover:text-accent-dark font-medium transition-colors duration-200">
                                                    View Details
                                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                            d="M9 5l7 7-7 7" />
                                                    </svg>
                                                </span>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sticky Sidebar -->
            <div class="lg:col-span-1">
                <div class="sticky top-24">
                    <?php echo $__env->make('pincodes.partials.sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const searchInput = document.getElementById('stateSearch');
            const stateCards = document.querySelectorAll('.state-card');
            let noResultsMessage = null;

            searchInput.addEventListener('keyup', function () {
                const searchTerm = this.value.toLowerCase();
                let hasResults = false;

                stateCards.forEach(card => {
                    const stateName = card.querySelector('h3').textContent.toLowerCase();
                    const isVisible = stateName.includes(searchTerm);
                    card.style.display = isVisible ? '' : 'none';
                    if (isVisible) hasResults = true;
                });

                // Handle no results
                if (!hasResults) {
                    if (!noResultsMessage) {
                        noResultsMessage = document.createElement('div');
                        noResultsMessage.className = 'text-center py-8 text-gray-500 dark:text-gray-400';
                        noResultsMessage.textContent = 'No states found matching your search.';
                        document.getElementById('statesGrid').appendChild(noResultsMessage);
                    }
                    noResultsMessage.style.display = '';
                } else if (noResultsMessage) {
                    noResultsMessage.style.display = 'none';
                }
            });
        });
    </script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/pincodes/1-all-state-listing.blade.php ENDPATH**/ ?>