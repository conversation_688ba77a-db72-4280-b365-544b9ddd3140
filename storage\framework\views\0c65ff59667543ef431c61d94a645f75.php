<?php if(isset($tool)): ?>
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "SoftwareApplication",
  "name": "<?php echo e($tool->name); ?>",
  "description": "<?php echo e($tool->meta_description); ?>",
  "applicationCategory": "WebApplication",
  "operatingSystem": "All",
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "USD",
    "availability": "https://schema.org/InStock"
  },
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "4.8",
    "ratingCount": "<?php echo e($tool->reviews_count ?? 245); ?>"
  },
  "author": {
    "@type": "Organization",
    "name": "Pincodes India",
    "url": "<?php echo e(config('app.url')); ?>"
  },
  "potentialAction": {
    "@type": "UseAction",
    "target": "<?php echo e(url()->current()); ?>"
  },
  "screenshot": {
    "@type": "ImageObject",
    "url": "<?php echo e(asset('assets/images/tools/' . $tool->image_path)); ?>",
    "width": "1366",
    "height": "768",
    "caption": "Screenshot of <?php echo e($tool->name); ?>"
  },
  "keywords": "<?php echo e($tool->meta_keywords); ?>",
  "datePublished": "<?php echo e($tool->created_at->format('Y-m-d')); ?>",
  "dateModified": "<?php echo e($tool->updated_at->format('Y-m-d')); ?>"
}
</script>
<?php endif; ?> <?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/layouts/partials/tools-json-ld.blade.php ENDPATH**/ ?>