<?php $__env->startSection('json-ld'); ?>
    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": "<?php echo e($pageTitle); ?>",
            "description": "<?php echo e($metaDescription); ?>",
            "publisher": {
                "@type": "Organization",
                "name": "NSK Multiservices Kosbi"
            },
            "url": "<?php echo e(url()->current()); ?>",
            "datePublished": "2024-07-04",
            "dateModified": "2024-07-04"
        }
    </script>

    <?php if(!empty($breadcrumbs)): ?>
        <script type="application/ld+json">
            {
                "@context": "https://schema.org",
                "@type": "BreadcrumbList",
                "itemListElement": [
                    <?php $__currentLoopData = $breadcrumbs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $breadcrumb): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        {
                            "@type": "ListItem",
                            "position": <?php echo e($index + 1); ?>,
                            "name": "<?php echo e($breadcrumb['name']); ?>",
                            "item": "<?php echo e($breadcrumb['url']); ?>"
                        } <?php if(!$loop->last): ?> , <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                ]
            }
        </script>
    <?php endif; ?>

    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": "NSK Multiservices Kosbi",
            "url": "<?php echo e(url()->current()); ?>",
            "logo": "<?php echo e(asset('assets/images/nsk/nsk-multiservices-logo.webp')); ?>",
            "contactPoint": {
                "@type": "ContactPoint",
                "telephone": "+91-9834754391",
                "contactType": "Customer Service"
            },
            "sameAs": [
                "https://www.linkedin.com/in/nsk-multiservices/",
                "https://www.instagram.com/nskmultiservices/",
                "https://x.com/digi_nsk",
                "https://www.facebook.com/nskmultiservices/"
            ]
        }
    </script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <?php if (isset($component)) { $__componentOriginal269900abaed345884ce342681cdc99f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal269900abaed345884ce342681cdc99f6 = $attributes; } ?>
<?php $component = App\View\Components\Breadcrumb::resolve(['segments' => $breadcrumbs,'pageTitle' => $pageTitle] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Breadcrumb::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal269900abaed345884ce342681cdc99f6)): ?>
<?php $attributes = $__attributesOriginal269900abaed345884ce342681cdc99f6; ?>
<?php unset($__attributesOriginal269900abaed345884ce342681cdc99f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal269900abaed345884ce342681cdc99f6)): ?>
<?php $component = $__componentOriginal269900abaed345884ce342681cdc99f6; ?>
<?php unset($__componentOriginal269900abaed345884ce342681cdc99f6); ?>
<?php endif; ?>
    
    <div class="container max-w-6xl mx-auto px-4">
        <!-- Hero Section -->
        <div class="relative bg-gradient-to-r from-primary-light to-primary-dark dark:from-primary-dark dark:to-primary-light rounded-3xl overflow-hidden mb-12 border border-border-light dark:border-border-dark shadow-sm">
            <div class="absolute inset-0 bg-grid-white/20 [mask-image:linear-gradient(0deg,white,transparent)]"></div>
            <div class="relative px-6 py-16 sm:py-24 sm:px-12">
                <div class="text-center max-w-3xl mx-auto">
                    <h2 class="text-4xl sm:text-5xl font-bold text-white mb-6"><?php echo e($pageTitle); ?></h2>
                    <p class="text-xl text-white/90"><?php echo e($metaDescription); ?></p>
                </div>
            </div>
            <div class="absolute inset-0 bg-gradient-to-t from-white/30 dark:from-bg-dark/30 to-transparent"></div>
        </div>

        <!-- Tools Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php $__empty_1 = true; $__currentLoopData = $tools; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="group relative bg-white dark:bg-bg-dark rounded-2xl shadow-sm hover:shadow-md dark:hover:shadow-lg border border-border-light dark:border-border-dark transition-all duration-300 overflow-hidden">
                    <!-- Tool Image with Overlay -->
                    <div class="relative aspect-[4/3] overflow-hidden">
                        <img class="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-300" 
                             src="<?php echo e(uploads_url($item->image_path)); ?>"
                             alt="<?php echo e($item->name); ?>">
                        <div class="absolute inset-0 bg-gradient-to-t from-gray-800/40 dark:from-gray-900/60 to-transparent"></div>
                    </div>

                    <!-- Content -->
                    <div class="relative p-6">
                        <!-- Tool Category Badge -->
                        <div class="absolute -top-4 left-6">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-accent-light/20 dark:bg-accent-dark/20 text-accent-light dark:text-accent-dark border border-accent-light/30 dark:border-accent-dark/30">
                                <?php echo e($item->category ?? 'Tool'); ?>

                            </span>
                        </div>

                        <!-- Tool Info -->
                        <div class="mt-2">
                            <h3 class="text-xl font-bold text-text-primary-light dark:text-text-primary-dark mb-3">
                                <?php echo e(Str::limit(__($item->name), 40)); ?>

                            </h3>
                            <p class="text-text-secondary-light dark:text-text-secondary-dark mb-6 line-clamp-2">
                                <?php echo e(Str::limit(strip_tags(__($item->meta_description)), 90)); ?>

                            </p>

                            <!-- Action Buttons -->
                            <div class="flex items-center justify-between">
                                <a href="<?php echo e(url("/tools/{$item->slug}")); ?>" 
                                   class="inline-flex items-center px-4 py-2 bg-primary-light dark:bg-primary-dark hover:bg-primary-dark dark:hover:bg-primary-light text-white font-medium rounded-lg transition-colors duration-200 group">
                                    Try Now
                                    <svg class="w-4 h-4 ml-2 transform group-hover:translate-x-1 transition-transform" 
                                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                              d="M14 5l7 7m0 0l-7 7m7-7H3"/>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-span-full">
                    <div class="text-center py-12 bg-bg-light dark:bg-bg-dark rounded-2xl border border-border-light dark:border-border-dark">
                        <svg class="mx-auto h-12 w-12 text-text-secondary-light dark:text-text-secondary-dark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <h2 class="mt-4 text-2xl font-bold text-text-primary-light dark:text-text-primary-dark"><?php echo e(__($emptyMessage)); ?></h2>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        // Add smooth hover effect for tool cards
        document.querySelectorAll('.group').forEach(card => {
            card.addEventListener('mousemove', e => {
                const rect = card.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                card.style.transform = `perspective(1000px) rotateX(${(y - rect.height/2)/20}deg) rotateY(${-(x - rect.width/2)/20}deg)`;
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'perspective(1000px) rotateX(0) rotateY(0)';
            });
        });
    </script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views/tools/tools.blade.php ENDPATH**/ ?>